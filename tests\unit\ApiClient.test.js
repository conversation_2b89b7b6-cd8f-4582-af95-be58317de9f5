// @ts-check
const { test, expect } = require('@playwright/test');

// Import the module under test
const ApiClient = require('../../src/api/ApiClient');

// Since we can't easily mock modules in Playwright tests without Jest,
// we'll test the behavior more directly by overriding methods
// and using simple counters instead of complex spies.

test.describe('ApiClient', () => {
  // Store original methods to restore after tests
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;
  
  // Simple counters and collectors
  let consoleLogMessages;
  let consoleErrorMessages;
  let fetchCalls;
  let fetchMockImplementation;
  
  // Mock fetch module
  const mockFetch = async (url, options) => {
    fetchCalls.push({ url, options });
    return fetchMockImplementation(url, options);
  };
  
  test.beforeEach(() => {
    // Reset counters and collectors
    consoleLogMessages = [];
    consoleErrorMessages = [];
    fetchCalls = [];
    
    // Mock console methods
    console.log = function(...args) {
      consoleLogMessages.push(args.join(' '));
    };
    
    console.error = function(...args) {
      consoleErrorMessages.push(args.join(' '));
    };
    
    // Default fetch mock implementation - successful response
    fetchMockImplementation = async () => {
      return {
        ok: true,
        status: 200,
        json: async () => ({ success: true, data: 'test data' }),
        text: async () => 'Success'
      };
    };
    
    // Mock environment variables
    process.env.API_BASE_URL = 'https://api.example.com';
    process.env.ADMIN_USERNAME = 'test-admin';
    process.env.ADMIN_PASSWORD = 'test-password';
  });
  
  test.afterEach(() => {
    // Restore original methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  test('get should propagate status code in error message on failure', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return an error
    fetchMockImplementation = async () => {
      return {
        ok: false,
        status: 404,
        text: async () => 'Not Found'
      };
    };
    
    // Act & Assert
    await expect(apiClient.get('/test-endpoint')).rejects.toThrow();
    
    // Verify error message contains status code
    expect(consoleErrorMessages.join(' ')).toContain('404');
    
    // Verify error is logged but doesn't contain sensitive information
    const errorLogs = consoleErrorMessages.join(' ');
    expect(errorLogs).toContain('404');
    expect(errorLogs).not.toContain(process.env.ADMIN_PASSWORD);
  });

  test('post should propagate status code in error message on failure', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return an error
    fetchMockImplementation = async () => {
      return {
        ok: false,
        status: 400,
        text: async () => 'Bad Request'
      };
    };
    
    // Act & Assert
    await expect(apiClient.post('/test-endpoint', { test: 'data' })).rejects.toThrow();
    
    // Verify error message contains status code
    expect(consoleErrorMessages.join(' ')).toContain('400');
    
    // Verify error is logged but doesn't contain sensitive information
    const errorLogs = consoleErrorMessages.join(' ');
    expect(errorLogs).toContain('400');
    expect(errorLogs).not.toContain(process.env.ADMIN_PASSWORD);
  });

  test('put should propagate status code in error message on failure', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return an error
    fetchMockImplementation = async () => {
      return {
        ok: false,
        status: 403,
        text: async () => 'Forbidden'
      };
    };
    
    // Act & Assert
    const error = await expect(apiClient.put('/test-endpoint', { test: 'data' })).rejects.toThrow();
    
    // Verify error message contains status code
    expect(error.message).toContain('403');
    
    // Verify error is logged but doesn't contain sensitive information
    const errorLogs = consoleErrorMessages.join(' ');
    expect(errorLogs).toContain('403');
    expect(errorLogs).not.toContain(process.env.ADMIN_PASSWORD);
  });

  test('delete should propagate status code in error message on failure', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return an error
    fetchMockImplementation = async () => {
      return {
        ok: false,
        status: 500,
        text: async () => 'Internal Server Error'
      };
    };
    
    // Act & Assert
    const error = await expect(apiClient.delete('/test-endpoint')).rejects.toThrow();
    
    // Verify error message contains status code
    expect(error.message).toContain('500');
    
    // Verify error is logged but doesn't contain sensitive information
    const errorLogs = consoleErrorMessages.join(' ');
    expect(errorLogs).toContain('500');
    expect(errorLogs).not.toContain(process.env.ADMIN_PASSWORD);
  });

  test('authenticate should throw error without leaking credentials on failure', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return an error
    fetchMockImplementation = async () => {
      return {
        ok: false,
        status: 401,
        text: async () => 'Unauthorized'
      };
    };
    
    // Act & Assert
    const error = await expect(apiClient.authenticate('test-user', 'test-password')).rejects.toThrow();
    
    // Verify error message contains status code but not credentials
    expect(error.message).toContain('401');
    expect(error.message).not.toContain('test-password');
    
    // Verify error is logged but doesn't contain credentials
    const errorLogs = consoleErrorMessages.join(' ');
    expect(errorLogs).toContain('401');
    expect(errorLogs).not.toContain('test-password');
    
    // Verify request was made with correct URL
    expect(fetchCalls.length).toBe(1);
    expect(fetchCalls[0].url).toContain('/api/v2/admin/login_check');
    
    // Verify credentials were sent in request body but not logged
    const requestBody = JSON.parse(fetchCalls[0].options.body);
    expect(requestBody).toHaveProperty('username', 'test-user');
    expect(requestBody).toHaveProperty('password', 'test-password');
    expect(errorLogs).not.toContain(JSON.stringify(requestBody));
  });

  test('authenticate should set token on successful authentication', async () => {
    // Arrange
    const apiClient = new ApiClient();
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Configure fetch to return a successful response with token
    fetchMockImplementation = async () => {
      return {
        ok: true,
        status: 200,
        json: async () => ({ token: 'test-token' })
      };
    };
    
    // Act
    await apiClient.authenticate('test-user', 'test-password');
    
    // Assert
    expect(apiClient.token).toBe('test-token');
    expect(apiClient.headers.Authorization).toBe('Bearer test-token');
  });

  test('get should automatically authenticate if useAuth is true and no token exists', async () => {
    // Arrange
    const apiClient = new ApiClient({ useAuth: true });
    
    // Mock getFetch to return our mock fetch
    apiClient.getFetch = async () => mockFetch;
    
    // Track authentication calls
    let authCalled = false;
    const originalAuthenticate = apiClient.authenticate;
    apiClient.authenticate = async (username, password) => {
      authCalled = true;
      apiClient.token = 'test-token';
      apiClient.headers.Authorization = 'Bearer test-token';
    };
    
    // Act
    await apiClient.get('/test-endpoint');
    
    // Assert
    expect(authCalled).toBe(true);
    expect(fetchCalls.length).toBe(1);
    expect(fetchCalls[0].options.headers.Authorization).toBe('Bearer test-token');
    
    // Restore original authenticate method
    apiClient.authenticate = originalAuthenticate;
  });
});