/**
 * @fileoverview Aeons layout and visual verification tests
 * @tags @regression @layout @visual
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { HomePage } = require('../../src/pages/general/HomePage');
const { AnalysisOrchestrator } = require('../../src/utils/gemini/service/AnalysisOrchestrator');
const { GeminiService } = require('../../src/utils/gemini/service/GeminiService');
const { VisualAnalysisHelper } = require('../../src/utils/visual-analisys-helper');
const fs = require('fs').promises;
const path = require('path');

test.describe('Aeons Website Layout Verification', () => {
    test.describe('Layout & Visual Verification Tests @visual', () => {
        /**
         * @description Verifies that there is no empty space between ritual text and 'read more' button
         * on the Sunset Soothe product page in mobile view.
         * @defect Asana Task #1208802967325896
         * @summary Empty space appears between the text and 'Read More' button in the 'An evening
         * ritual from Ancient China' section on mobile view.
         */
        test('Verify no gap between ritual text and read more button on mobile', async ({ page, browserStackHelper, testDataManager }) => {
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Setup mobile viewport and navigate', async () => {
                // Set viewport to mobile size - BrowserStack will handle real device dimensions
                if (!process.env.BROWSERSTACK_REAL_DEVICE) {
                    await page.setViewportSize({ width: 375, height: 667 });
                }

                // Navigate to Sunset Soothe product page using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const productUrl = `${baseUrl}/products/aeons-sunset-soothe`;

                await page.goto(productUrl);
                await page.waitForLoadState('networkidle');

                // Wait for page to stabilize
                await browserStackHelper.waitForVisualStability(page);

                // Take screenshot after page loads
                await browserStackHelper.takeScreenshot(page, 'sunset-soothe-page-loaded');
            });

            await test.step('Scroll to the ritual section and capture screenshot', async () => {
                // Scroll to the ritual section
                const ritualSection = page.locator('p.h1:has-text("An evening ritual from Ancient China")');
                await ritualSection.scrollIntoViewIfNeeded();

                // Wait for any animations to complete
                await page.waitForTimeout(1000);

                // Take screenshot of ritual section
                await browserStackHelper.takeScreenshot(page, 'ritual-section-before-analysis');

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'sunset-soothe-ritual-section',
                    'ritual-read-more-spacing',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'mobile'
                    }
                );

                console.log('Screenshot uploaded to Cloudinary:', metadata.cloudinaryUrl);

                // Use Gemini AI to analyze the spacing via AnalysisOrchestrator
                const analysisPrompt =
                    "Analyze this mobile screenshot of the 'An evening ritual from Ancient China' section. " +
                    "Focus specifically on the spacing between the text content and the 'Read More' button. " +
                    "There should be no large empty space between the text and the button. " +
                    "The layout should appear cohesive with consistent spacing. " +
                    "Indicate clearly if there is excessive empty space between the ritual text and the 'Read More' button.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Sunset Soothe Ritual Section Spacing Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                    console.log('Gemini Analysis:', visualAnalysisText);
                }

                // Check if the analysis confirms proper spacing
                const hasProperSpacing =
                    visualAnalysisText.toLowerCase().includes('no excessive space') ||
                    visualAnalysisText.toLowerCase().includes('proper spacing') ||
                    visualAnalysisText.toLowerCase().includes('appropriate spacing') ||
                    visualAnalysisText.toLowerCase().includes('cohesive layout') ||
                    !visualAnalysisText.toLowerCase().includes('large gap') ||
                    !visualAnalysisText.toLowerCase().includes('excessive space');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has proper spacing:', hasProperSpacing);

                expect(hasProperSpacing,
                    'There should be no excessive empty space between ritual text and Read More button'
                ).toBeTruthy();
            });
        });

        /**
         * @description Verifies that the ritual icons in the 'Explore our other rituals' section
         * are displayed uniformly on the Legacy of Babilonia page in mobile view.
         * @defect Asana Task #1208802967325906
         * @summary Other rituals icons are not uniform in size and appearance in the
         * 'Explore our other rituals' section on mobile view.
         */
        test('Verify uniform ritual icons on Legacy of Babilonia page', async ({ page, browserStackHelper, testDataManager }) => {
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Setup mobile viewport and navigate', async () => {
                // Set viewport to mobile size - BrowserStack will handle real device dimensions
                if (!process.env.BROWSERSTACK_REAL_DEVICE) {
                    await page.setViewportSize({ width: 375, height: 667 });
                }

                // Navigate to Legacy of Babilonia ritual page using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const ritualUrl = `${baseUrl}/ritual/legacy-of-babylonia`;

                await page.goto(ritualUrl);
                await page.waitForLoadState('networkidle');

                // Wait for page to stabilize
                await browserStackHelper.waitForVisualStability(page);

                // Take screenshot after page loads
                await browserStackHelper.takeScreenshot(page, 'legacy-page-loaded');
            });

            await test.step('Scroll to the explore rituals section and capture screenshot', async () => {
                // Scroll to the other rituals section
                const otherRitualsSection = page.locator('text=Explore our other rituals');
                await otherRitualsSection.scrollIntoViewIfNeeded();

                // Wait for any animations to complete
                await page.waitForTimeout(1000);

                // Take screenshot of other rituals section
                await browserStackHelper.takeScreenshot(page, 'other-rituals-section');

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'legacy-babilonia-other-rituals',
                    'ritual-icons-uniformity',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'mobile'
                    }
                );

                console.log('Screenshot uploaded to Cloudinary:', metadata.cloudinaryUrl);

                // Use Gemini AI to analyze the ritual icons via AnalysisOrchestrator
                const analysisPrompt =
                    "Analyze this mobile screenshot of the 'Explore our other rituals' section. " +
                    "Focus specifically on the ritual icons/images displayed. " +
                    "These icons should be uniform in size, style, and quality. " +
                    "Check if all ritual icons have consistent dimensions, similar visual style, " +
                    "and appear properly aligned with each other. " +
                    "Indicate clearly if there are any inconsistencies in size, style, or alignment of these ritual icons.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Legacy of Babilonia Ritual Icons Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                }

                // Check if the analysis confirms uniformity
                const hasUniformIcons =
                    visualAnalysisText.toLowerCase().includes('uniform') ||
                    visualAnalysisText.toLowerCase().includes('consistent') ||
                    visualAnalysisText.toLowerCase().includes('properly aligned') ||
                    !visualAnalysisText.toLowerCase().includes('inconsistent') ||
                    !visualAnalysisText.toLowerCase().includes('not uniform');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has uniform icons:', hasUniformIcons);

                expect(hasUniformIcons,
                    'The ritual icons should be uniform in size and appearance'
                ).toBeTruthy();
            });
        });

        /**
         * @description Verifies that the gap in the 'Explore rituals of the ancient world' section
         * on the homepage has been reduced to match the design specification.
         * @defect Asana Task #1208824928563675
         * @summary Gap in the 'Explore rituals of the ancient world' section is larger than
         * specified in the design, causing inconsistent spacing on the homepage.
         */
        test('Verify reduced gap in Explore rituals section on homepage', async ({ page, browserStackHelper, testDataManager }) => {
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Navigate to homepage', async () => {
                // Navigate to homepage using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();

                await page.goto(baseUrl);
                await page.waitForLoadState('networkidle');

                // Wait for page to stabilize
                await browserStackHelper.waitForVisualStability(page);

                // Take screenshot of homepage
                await browserStackHelper.takeScreenshot(page, 'homepage-loaded');
            });

            await test.step('Scroll to Explore rituals section and capture screenshot', async () => {
                // Scroll to the explore rituals section
                const exploreSection = page.locator('text=Explore rituals of the ancient world');
                await exploreSection.scrollIntoViewIfNeeded();

                // Wait for any animations to complete
                await page.waitForTimeout(1000);

                // Take screenshot of explore section
                await browserStackHelper.takeScreenshot(page, 'explore-rituals-section');

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'homepage-explore-rituals',
                    'explore-section-gap',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'desktop'
                    }
                );

                console.log('Screenshot uploaded to Cloudinary:', metadata.cloudinaryUrl);

                // Use Gemini AI to analyze the spacing via AnalysisOrchestrator
                const analysisPrompt =
                    "Analyze this screenshot of the 'Explore rituals of the ancient world' section on the homepage. " +
                    "Focus on the spacing/gap between the heading and the content below it. " +
                    "The gap should be reduced and consistent with the design, not excessive. " +
                    "Assess if the spacing appears appropriate and balanced, not too large. " +
                    "Indicate clearly if there is excessive space or if the spacing appears appropriate.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Homepage Explore Section Spacing Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                }

                // Check if the analysis confirms appropriate spacing
                const hasApproprateSpacing =
                    visualAnalysisText.toLowerCase().includes('appropriate spacing') ||
                    visualAnalysisText.toLowerCase().includes('balanced spacing') ||
                    visualAnalysisText.toLowerCase().includes('consistent with design') ||
                    !visualAnalysisText.toLowerCase().includes('excessive gap') ||
                    !visualAnalysisText.toLowerCase().includes('too large');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has appropriate spacing:', hasApproprateSpacing);

                expect(hasApproprateSpacing,
                    'The gap in the Explore rituals section should be reduced to match design specifications'
                ).toBeTruthy();
            });
        });

        /**
         * @description Verifies the implementation of Apple Cider Vinegar product pages is correct.
         * @defect Asana Task #1208824928563680
         * @summary Product pages for Apple Cider Vinegar products need to be checked for correct implementation
         * across both product pages and category pages.
         */
        test('Verify Apple Cider Vinegar pages implementation', async ({ page, browserStackHelper, testDataManager }) => {
            const analysisOrchestrator = new AnalysisOrchestrator({
                includeVisualAnalysis: true
            });

            await test.step('Verify Golden Harvest product page', async () => {
                // Navigate to Golden Harvest product page using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const productUrl = `${baseUrl}/products/aeons-golden-harvest`;

                await page.goto(productUrl);
                await page.waitForLoadState('networkidle');

                // Wait for page to stabilize
                await browserStackHelper.waitForVisualStability(page);

                // Take screenshot of Golden Harvest product page
                await browserStackHelper.takeScreenshot(page, 'golden-harvest-product-page');

                // Verify key elements on page
                await expect(page.locator('.main p.title')).toContainText('Golden Harvest');
                await expect(page.locator('.section1 .info-box .note')).toBeVisible();

                // Check for Apple Cider Vinegar mention in the description
                const descriptionText = await page.locator('.section1 .info-box .note').textContent();
                expect(descriptionText.toLowerCase().includes('apple cider vinegar')).toBeTruthy();

                // Verify product images
                await expect(page.locator('#carousel-thumbnail #carousel-thumbnail-list').first()).toBeVisible();

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'golden-harvest-acv-product',
                    'product-implementation',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'desktop'
                    }
                );

                // Use Gemini AI to analyze the implementation
                const analysisPrompt =
                    "Analyze this screenshot of the Golden Harvest Apple Cider Vinegar product page. " +
                    "Verify that the page appears professionally implemented with: " +
                    "1. Clear product title and branding " +
                    "2. High-quality product images " +
                    "3. Well-formatted product description " +
                    "4. Appropriate pricing and purchase options " +
                    "Indicate if there are any implementation issues or if the page appears correctly implemented.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Golden Harvest ACV Page Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                    console.log('Gemini Analysis:', visualAnalysisText);
                }

                // Check if the analysis confirms good implementation
                const hasGoodImplementation =
                    visualAnalysisText.toLowerCase().includes('professionally implemented') ||
                    visualAnalysisText.toLowerCase().includes('well-designed') ||
                    visualAnalysisText.toLowerCase().includes('correctly implemented') ||
                    !visualAnalysisText.toLowerCase().includes('implementation issues');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has good implementation:', hasGoodImplementation);

                expect(hasGoodImplementation,
                    'The Golden Harvest product page should be correctly implemented'
                ).toBeTruthy();
            });

            await test.step('Verify Legacy of Babylonia category page', async () => {
                // Navigate to Legacy of Babylonia category page using centralized URL management
                const baseUrl = testDataManager.getBaseUrl();
                const categoryUrl = `${baseUrl}/ritual/legacy-of-babylonia`;

                await page.goto(categoryUrl);
                await page.waitForLoadState('networkidle');

                // Wait for page to stabilize
                await browserStackHelper.waitForVisualStability(page);

                // Take screenshot of category page
                await browserStackHelper.takeScreenshot(page, 'legacy-babylonia-category-page');

                // Verify key elements on page
                await expect(page.locator('.section1 p.h1')).toContainText('Legacy of Babylonia');

                // Verify Golden Harvest product appears in the collection
                const productCards = page.locator('.item-box');
                await expect(productCards).toBeVisible();

                // Check if the Golden Harvest product is listed
                const goldHarvestCard = page.locator('.item-box:has-text("GOLDEN HARVEST")');
                await expect(goldHarvestCard).toBeVisible();

                // Take screenshot and upload to Cloudinary
                const metadata = await VisualAnalysisHelper.captureAndUploadScreenshot(
                    page,
                    'babylonia-collection-acv',
                    'category-implementation',
                    {
                        fullPage: false,
                        platform: process.env.PLATFORM || 'desktop'
                    }
                );

                // Use Gemini AI to analyze the implementation
                const analysisPrompt =
                    "Analyze this screenshot of the Legacy of Babylonia category page that includes Apple Cider Vinegar products. " +
                    "Verify that the page appears professionally implemented with: " +
                    "1. Clear category title and description " +
                    "2. Properly arranged product cards/grid " +
                    "3. Consistent product card styling " +
                    "4. Visible product titles and prices " +
                    "Indicate if there are any implementation issues or if the page appears correctly implemented.";

                const analysisResults = await analysisOrchestrator.analyze(
                    {
                        title: 'Babylonia Category Page Analysis',
                        status: 'passed'
                    },
                    {
                        screenshots: [metadata.cloudinaryUrl],
                        customPrompts: {
                            visual: analysisPrompt
                        }
                    }
                );

                // Extract the visual analysis text
                let visualAnalysisText = '';
                if (analysisResults.visual && analysisResults.visual.length > 0) {
                    visualAnalysisText = analysisResults.visual[0].analysis;
                }

                // Check if the analysis confirms good implementation
                const hasGoodImplementation =
                    visualAnalysisText.toLowerCase().includes('professionally implemented') ||
                    visualAnalysisText.toLowerCase().includes('well-organized') ||
                    visualAnalysisText.toLowerCase().includes('correctly implemented') ||
                    !visualAnalysisText.toLowerCase().includes('implementation issues');

                console.log('Gemini Analysis:', visualAnalysisText);
                console.log('Has good implementation:', hasGoodImplementation);

                expect(hasGoodImplementation,
                    'The Legacy of Babylonia category page should be correctly implemented'
                ).toBeTruthy();
            });
        });
    });
});
