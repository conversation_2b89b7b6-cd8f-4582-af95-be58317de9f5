# AEONS Brand Testing Guide

## Overview

AEONS is the primary brand in the testing framework with the most comprehensive test coverage. This guide provides specific instructions for testing AEONS brand functionality, including unique features, data configurations, and specialized test scenarios.

## Brand Configuration

### Environment URLs

```bash
# Development
AEONS_DEV_URL=https://aeons-dev.info

# Staging (Default)
AEONS_STAGE_URL=https://aeonstest.info

# Production
AEONS_PROD_URL=https://aeons.co.uk
```

### Admin Panel Access

```bash
# Admin panel URLs
AEONS_ADMIN_DEV=https://admin.aeons-dev.info
AEONS_ADMIN_STAGE=https://admin.aeonstest.info
AEONS_ADMIN_PROD=https://admin.aeons.co.uk
```

## AEONS-Specific Test Data

### Product Catalog

AEONS products are defined in `tests/data/brands/aeons/products.yml`:

**Primary Products:**
- `sunrise_spark` - Morning energy supplement
- `sunset_soothe` - Evening relaxation supplement
- `ancient_roots` - Olive oil product line
- `total_harmony` - Complete wellness package
- `heart_of_the_hadza` - Traditional supplement
- `legacy_of_babylonia` - Heritage product line
- `natures_gift_bone_broth` - Bone broth supplements
- `golden_harvest` - Premium product line

**Product Features:**
- Multiple flavors per product (classic, lemon, truffle)
- Subscription and one-time purchase options
- Quantity-based pricing tiers
- Upsell and cross-sell capabilities

### Payment Methods

AEONS supports comprehensive payment testing:

```yaml
# Credit Card Testing
stripe_valid: "****************"
stripe_valid_3dsecure: "****************"
stripe_expired: "****************" (12/20)
stripe_invalid: "****************"

# PayPal Testing
paypal_sandbox: Configured with test credentials
paypal_invalid: For negative testing
```

### User Profiles

```yaml
default:
  email: "<EMAIL>"
  firstName: "Alice"
  lastName: "Johnson"
  country: "GB"
  # Full address details for UK testing
```

## AEONS-Specific Test Execution

### Core Business Flows

#### 1. Main Purchase Flow

```bash
# Standard one-time purchase
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Mobile purchase testing
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true

# With specific product
PRODUCT_SLUG=sunrise_spark node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

#### 2. Subscription Management

```bash
# Subscription purchase
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Subscription renewal testing
node run-test.js tests/regression/subscription-renewal.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Payment failure handling
node run-test.js tests/regression/subscription-renewal-payment-failure.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

#### 3. Sales Funnel Testing

```bash
# Complete sales funnel with upsell
node run-test.js tests/regression/sales-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Backend verification
node run-test.js tests/regression/sales-funnel-backend-complete.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Purchase funnel upsell
node run-test.js tests/regression/purchase-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

### AEONS-Specific Features

#### 1. Multi-Flavor Product Testing

```bash
# Test different product flavors
for flavor in classic lemon truffle; do
  PRODUCT_FLAVOR=$flavor node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage
done
```

#### 2. Subscription vs One-Time Purchase

```bash
# One-time purchase
PURCHASE_TYPE=oneTime node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Subscription purchase
PURCHASE_TYPE=subscription node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

#### 3. Quantity-Based Pricing

```bash
# Test different quantity tiers
for quantity in minimum medium maximum; do
  QUANTITY_TIER=$quantity node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage
done
```

## AEONS Admin Panel Testing

### Admin Access

```bash
# Set admin credentials for AEONS
export ADMIN_USER=your_aeons_admin_username
export ADMIN_PASSWORD=your_aeons_admin_password
```

### Sales Funnel Management

```bash
# Test sales funnel creation and management
node run-test.js tests/regression/sales-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Verify funnel links and configuration
node run-test.js tests/regression/sales-funnel-backend-complete.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

### Order Management

```bash
# Test order verification and management
node run-test.js tests/examples/order-verification-example.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

## AEONS Email Testing

### Order Confirmation Emails

```bash
# Test order confirmation email flow
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Verify email content and branding
node run-test.js tests/regression/abandoned-cart-email.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

### Email Templates

AEONS uses brand-specific email templates:
- Order confirmation with AEONS branding
- Abandoned cart recovery emails
- Subscription renewal notifications
- Payment failure alerts

## AEONS Database Testing

### Database Schema

AEONS uses the following key database tables:
- `orders` - Order information
- `subscriptions` - Subscription management
- `customers` - Customer data
- `products` - Product catalog
- `sales_funnels` - Funnel configuration

### Database Test Execution

```bash
# Tests requiring database access
node run-test.js tests/regression/subscription-renewal.spec.js --platform=windows-chrome --brand=aeons --env=stage
node run-test.js tests/regression/sales-funnel-backend-complete.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

## AEONS Visual Testing

### Brand-Specific Visual Elements

```bash
# Test AEONS visual branding
node run-test.js tests/visual/visual.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Responsive design testing
node run-test.js tests/regression/responsive.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

### Visual Regression Testing

```bash
# Compare visual elements across environments
node run-test.js tests/regression/layout-verification.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

## AEONS Performance Testing

### Load Testing Considerations

```bash
# Single session testing (BrowserStack limitation)
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage --workers=1

# Extended timeout for complex flows
node run-test.js tests/regression/sales-funnel-backend-complete.spec.js --platform=windows-chrome --brand=aeons --env=stage --timeout=300000
```

## AEONS-Specific Troubleshooting

### Common Issues

1. **Product Not Found Errors**
   ```bash
   # Check available AEONS products
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'aeons', 'stage');
   console.log('AEONS products:', Object.keys(manager.productsData));
   "
   ```

2. **Sales Funnel Issues**
   ```bash
   # Verify admin access
   curl -u "$ADMIN_USER:$ADMIN_PASSWORD" https://admin.aeonstest.info/api/health
   
   # Check funnel configuration
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'aeons', 'stage');
   console.log('Funnel configs:', manager.testData.funnel_configs);
   "
   ```

3. **Subscription Testing Issues**
   ```bash
   # Verify database connection for subscription tests
   node -e "
   const { DatabaseUtils } = require('./src/utils/DatabaseUtils');
   const db = new DatabaseUtils();
   db.testConnection().then(() => {
     console.log('✅ Database connection successful');
   }).catch(error => {
     console.log('❌ Database connection failed:', error.message);
   });
   "
   ```

## AEONS Test Automation Best Practices

### 1. Test Data Management

- Use TestDataManager for all data access
- Leverage YAML configuration for flexibility
- Maintain environment-specific overrides

### 2. Test Execution Strategy

- Start with smoke tests for quick validation
- Use desktop platforms for complex admin flows
- Use real mobile devices for mobile-specific testing

### 3. Monitoring and Reporting

- Monitor BrowserStack session usage
- Track test execution times
- Maintain test result archives

## AEONS CI/CD Integration

### GitLab CI/CD Commands

```bash
# AEONS-specific CI commands
npm run ci:test:stage:chrome -- --brand=aeons
npm run ci:test:stage:android:bs -- --brand=aeons

# Sequential testing for AEONS
node ci/run-sequential-tests.js stage tests/regression/main-purchase.spec.js @stage_one_time_smoke windows-chrome --brand=aeons
```

### Environment-Specific Testing

```bash
# Development environment
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=dev --tags=@smoke

# Staging environment (default)
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@regression

# Production environment (limited tests)
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=prod --tags=@smoke --dataset=production
```

## Next Steps

1. **Review** the main [Test Catalog](../test-catalog.md) for complete test descriptions
2. **Check** [DSS Testing Guide](./dss-testing-guide.md) for comparison with other brands
3. **Consult** [Troubleshooting Guide](../troubleshooting.md) for issue resolution
4. **Follow** [Execution Guide](../execution-guide.md) for detailed command examples
