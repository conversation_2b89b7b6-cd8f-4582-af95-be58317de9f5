# DSS Brand Test Data for Critical Test Flows
test_users:
  default:
    firstName: <PERSON><PERSON><PERSON>
    lastName: Skripka
    email: <EMAIL>
    phone: "+44984570736"
    address: "83 Ducie Street, aptx, aptx"
    address2: ""
    city: Manchester
    postcode: "M1 2JQ"
    county: Manchester
    country: GB
  paypal_test:
    firstName: <PERSON><PERSON><PERSON>
    lastName: Skripka
    email: <EMAIL>
    phone: "+44984570736"
    address: "83 Ducie Street, aptx, aptx"
    address2: "aptx"
    city: Manchester
    postcode: "M1 2JQ"
    county: Manchester
    country: GB
  subscription_test:
    firstName: <PERSON><PERSON><PERSON>
    lastName: Skripka
    email: <EMAIL>
    phone: "+44984570736"
    address: "83 Ducie Street, aptx, aptx"
    address2: "aptx"
    city: Manchester
    postcode: "M1 2JQ"
    county: Manchester
    country: GB
  sales_funnel_test:
    firstName: <PERSON><PERSON><PERSON>
    lastName: Skripka
    email: <EMAIL>
    phone: "+44984570736"
    address: "83 Ducie Street, aptx, aptx"
    address2: "aptx"
    city: Manchester
    postcode: "M1 2JQ"
    county: Manchester
    country: GB
  abandoned_cart_test:
    firstName: Karolis
    lastName: Skripka
    email: <EMAIL>
    phone: "+44984570736"
    address: "83 Ducie Street, aptx, aptx"
    address2: "aptx"
    city: Manchester
    postcode: "M1 2JQ"
    county: Manchester
    country: GB

payment_methods:
  stripe_valid:
    type: card
    cardNumber: "****************"
    expiry: "04/25"
    cvc: "424"
    zipCode: "M1 2JQ"
  stripe_valid_3dsecure:
    type: card
    number: "****************"
    expiry: "12/29"
    cvc: "123"
    zipCode: "M1 2JQ"
  stripe_invalid:
    type: card
    number: "****************"
    expiry: "12/29"
    cvc: "123"
    zipCode: "M1 2JQ"
  paypal_valid:
    email: "<EMAIL>"
    # Password is stored in environment variables

shipping_methods:
  GB:
    domestic_tracked:
      name: "Domestic tracked"
      code: "tracked_48"
      price: 2.95
    international_tracked:
      name: "International tracked"
      code: "tracked_international"
      price: 12.95

funnel_configs:
  demo_dsv_1:
    code: "demo-dsv-1"
    initial_product:
      name: "Dark Spot Vanish"
      price: 89.00
    upsell_product:
      name: "Relax + Restore"
      price: 10.00

email_templates:
  order_confirmation:
    subject: "Order #%ORDER_NUMBER% Is Confirmed | Dr. Sister Skincare"
  welcome_email:
    subject: "Welcome To Dr. Sister Skincare!"
  subscription_created:
    subject: "Your subscription has been created"
  abandoned_cart:
    subject: "You left something behind | Dr. Sister Skincare"