# Environment Setup Guide

## Overview

This guide provides comprehensive instructions for setting up the testing environment, including all required environment variables, secrets, and configuration files needed for the Playwright-BrowserStack multi-brand e-commerce testing framework.

## Prerequisites

### System Requirements

- **Node.js**: Version 16.0.0 or higher
- **npm**: Version 7.0.0 or higher
- **Git**: For repository management
- **Operating System**: Windows 10+, macOS 10.15+, or Linux Ubuntu 18.04+

### Account Requirements

- **BrowserStack Account**: Automate - Desktop & Mobile plan
- **Mailtrap Account**: For email testing (free tier available)
- **Cloudinary Account**: For visual testing (optional)
- **Google Cloud Account**: For Gemini AI integration (optional)

## Installation

### 1. Clone Repository and Install Dependencies

```bash
git clone <repository-url>
cd browserstack-playwright
npm install
```

### 2. Install Playwright Browsers (Local Testing)

```bash
npx playwright install
npx playwright install-deps
```

## Environment Variables Configuration

### 1. Create Environment File

Create a `.env` file in the project root:

```bash
touch .env
```

### 2. Core Framework Configuration

```env
# Test Configuration
BRAND=aeons                    # Default brand (aeons|dss|ypn)
TEST_ENV=stage                 # Default environment (dev|stage|prod)
TEST_DATA_SET=default          # Default data set (default|staging|production)
PLATFORM=windows-chrome        # Default platform
TEST_TIMEOUT=120000           # Test timeout in milliseconds
WORKERS=1                     # Number of parallel workers (keep at 1 for BrowserStack)

# Test Execution
USE_BROWSERSTACK=false        # Enable/disable BrowserStack by default
RETRY_COUNT=1                 # Number of test retries
TEST_WORKERS=1               # Playwright worker count
```

### 3. BrowserStack Configuration

```env
# BrowserStack Credentials (Required for BrowserStack testing)
BROWSERSTACK_USERNAME=your_username_here
BROWSERSTACK_ACCESS_KEY=your_access_key_here

# BrowserStack SDK Configuration
BROWSERSTACK_SDK_ENABLED=false    # Enable SDK mode
BROWSERSTACK_FORCED=false         # Force BrowserStack usage
BROWSERSTACK_DEBUG=false          # Enable debug logging
BROWSERSTACK_REAL_DEVICE=true     # Use real devices for mobile testing

# BrowserStack Build Configuration
BUILD_NAME=                       # Custom build name (auto-generated if empty)
PROJECT_NAME=                     # Project name for BrowserStack dashboard
```

### 4. Email Testing Configuration (Mailtrap)

```env
# Mailtrap Configuration (Required for email verification tests)
MAILTRAP_API_TOKEN=your_api_token_here
MAILTRAP_INBOX_ID=your_inbox_id_here
MAILTRAP_ACCOUNT_ID=your_account_id_here    # Optional, for advanced features
```

### 5. Database Configuration (SSH Tunnel)

```env
# SSH Tunnel Configuration (Required for database tests)
USE_SSH_TUNNEL=true
SSH_HOST=your_ssh_host_here
SSH_PORT=22
SSH_USER=your_ssh_username_here
SSH_PRIVATE_KEY_PATH=path/to/private/key    # Optional, if using key file
SSH_PRIVATE_KEY=-----BEGIN PRIVATE KEY-----  # Optional, inline private key

# Database Configuration
DB_HOST=your_database_host_here
DB_PORT=3306
DB_USER=your_database_username_here
DB_PASSWORD=your_database_password_here
DB_NAME=your_database_name_here
DB_SSL=false                      # Enable SSL for database connection
LOCAL_DB_PORT=3307               # Local port for SSH tunnel
```

### 6. Payment Testing Configuration

```env
# PayPal Sandbox Configuration (Required for PayPal tests)
PAYPAL_USERNAME=your_paypal_sandbox_username
PAYPAL_PASSWORD=your_paypal_sandbox_password
PAYPAL_SANDBOX_MODE=true

# Stripe Configuration (Usually handled via test data YAML)
STRIPE_TEST_MODE=true
```

### 7. Visual Testing Configuration

```env
# Cloudinary Configuration (Optional, for visual testing)
CLOUDINARY_CLOUD_NAME=your_cloud_name_here
CLOUDINARY_API_KEY=your_api_key_here
CLOUDINARY_API_SECRET=your_api_secret_here

# Gemini AI Configuration (Optional, for AI-powered visual analysis)
GOOGLE_AI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro-vision    # Default model for visual analysis
```

### 8. Admin Panel Configuration

```env
# Admin Panel Access (Required for admin tests)
ADMIN_USER=your_admin_username_here
ADMIN_PASSWORD=your_admin_password_here
ADMIN_BASE_URL=https://admin.your-domain.com
```

### 9. CI/CD Configuration

```env
# CI/CD Environment Variables
CI=false                         # Set to true in CI environment
CI_PIPELINE_SOURCE=              # GitLab CI pipeline source
CI_COMMIT_REF_NAME=              # Git branch name
CI_JOB_ID=                       # CI job identifier

# Artifact Management
DOWNLOAD_ARTIFACTS=true          # Download BrowserStack artifacts
ARTIFACT_RETENTION_DAYS=7        # How long to keep artifacts
```

## Account Setup Instructions

### BrowserStack Account Setup

1. **Sign up** at [BrowserStack.com](https://www.browserstack.com)
2. **Choose plan**: Automate - Desktop & Mobile (required for real device testing)
3. **Get credentials**:
   - Navigate to Account → Settings
   - Copy Username and Access Key
   - Add to `.env` file as `BROWSERSTACK_USERNAME` and `BROWSERSTACK_ACCESS_KEY`

4. **Verify setup**:
```bash
# Test BrowserStack connection
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" https://api.browserstack.com/automate/plan.json
```

### Mailtrap Account Setup

1. **Sign up** at [Mailtrap.io](https://mailtrap.io)
2. **Create inbox**:
   - Go to Email Testing → Inboxes
   - Create new inbox or use default
   - Note the Inbox ID

3. **Get API credentials**:
   - Go to Settings → API Tokens
   - Generate new token
   - Add to `.env` file as `MAILTRAP_API_TOKEN` and `MAILTRAP_INBOX_ID`

4. **Verify setup**:
```bash
# Test Mailtrap connection
curl -H "Authorization: Bearer $MAILTRAP_API_TOKEN" https://mailtrap.io/api/v1/inboxes
```

### Database Access Setup

1. **SSH Key Configuration**:
   - Generate SSH key pair if needed: `ssh-keygen -t rsa -b 4096`
   - Add public key to server's `~/.ssh/authorized_keys`
   - Configure private key in `.env` file

2. **Database Credentials**:
   - Obtain database connection details from system administrator
   - Configure SSH tunnel settings
   - Test connection manually before running tests

3. **Verify setup**:
```bash
# Test SSH connection
ssh -i /path/to/private/key username@ssh_host

# Test database connection through tunnel
mysql -h 127.0.0.1 -P 3307 -u db_user -p db_name
```

### Visual Testing Setup (Optional)

#### Cloudinary Setup

1. **Sign up** at [Cloudinary.com](https://cloudinary.com)
2. **Get credentials**:
   - Dashboard → Settings → Account
   - Copy Cloud Name, API Key, and API Secret
   - Add to `.env` file

#### Gemini AI Setup

1. **Google Cloud Console**:
   - Create project at [console.cloud.google.com](https://console.cloud.google.com)
   - Enable Gemini API
   - Create API key
   - Add to `.env` file as `GOOGLE_AI_API_KEY`

## Brand-Specific Configuration

### AEONS Configuration

```env
# AEONS-specific settings
AEONS_ADMIN_URL=https://admin.aeonstest.info
AEONS_STAGE_URL=https://aeonstest.info
AEONS_PROD_URL=https://aeons.co.uk
```

### DSS Configuration

```env
# DSS-specific settings
DSS_ADMIN_URL=https://admin.dss.crm-test.info
DSS_STAGE_URL=https://dss.crm-test.info
DSS_PROD_URL=https://drsisterskincare.com
DSS_SHOPIFY_URL=https://dss-shopify.example.com
```

### YPN Configuration

```env
# YPN-specific settings
YPN_ADMIN_URL=https://admin.ypntest.info
YPN_STAGE_URL=https://ypntest.info
YPN_PROD_URL=https://yourpetnutrition.com
```

## Configuration Validation

### 1. Environment Validation Script

Create and run validation script:

```bash
# Create validation script
cat > validate-env.js << 'EOF'
require('dotenv').config();

const requiredVars = [
  'BROWSERSTACK_USERNAME',
  'BROWSERSTACK_ACCESS_KEY',
  'MAILTRAP_API_TOKEN',
  'MAILTRAP_INBOX_ID'
];

const optionalVars = [
  'SSH_HOST',
  'DB_HOST',
  'CLOUDINARY_CLOUD_NAME',
  'GOOGLE_AI_API_KEY'
];

console.log('=== Environment Validation ===');

// Check required variables
let missingRequired = [];
requiredVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`✓ ${varName}: configured`);
  } else {
    console.log(`✗ ${varName}: MISSING`);
    missingRequired.push(varName);
  }
});

// Check optional variables
optionalVars.forEach(varName => {
  if (process.env[varName]) {
    console.log(`✓ ${varName}: configured (optional)`);
  } else {
    console.log(`- ${varName}: not configured (optional)`);
  }
});

if (missingRequired.length > 0) {
  console.log('\n❌ Missing required environment variables:');
  missingRequired.forEach(varName => console.log(`   - ${varName}`));
  process.exit(1);
} else {
  console.log('\n✅ All required environment variables are configured');
}
EOF

# Run validation
node validate-env.js
```

### 2. Test Framework Validation

```bash
# Validate TestDataManager
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'aeons', 'stage');
console.log('✅ TestDataManager validation passed');
console.log('Base URL:', manager.getBaseUrl());
"

# Validate BrowserStack connection
node -e "
const axios = require('axios');
const auth = Buffer.from(\`\${process.env.BROWSERSTACK_USERNAME}:\${process.env.BROWSERSTACK_ACCESS_KEY}\`).toString('base64');
axios.get('https://api.browserstack.com/automate/plan.json', {
  headers: { 'Authorization': \`Basic \${auth}\` }
}).then(response => {
  console.log('✅ BrowserStack connection validated');
  console.log('Plan:', response.data.automate_plan);
}).catch(error => {
  console.log('❌ BrowserStack connection failed:', error.message);
});
"
```

### 3. Quick Test Run

```bash
# Run a simple validation test
node run-test.js tests/validation/enhanced-fixture-validation.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

## Security Considerations

### 1. Environment File Security

```bash
# Ensure .env file is in .gitignore
echo ".env" >> .gitignore

# Set appropriate file permissions
chmod 600 .env
```

### 2. Credential Management

- **Never commit** credentials to version control
- **Use environment variables** in CI/CD systems
- **Rotate credentials** regularly
- **Use least privilege** principle for database access

### 3. SSH Key Security

```bash
# Set secure permissions for SSH keys
chmod 600 ~/.ssh/id_rsa
chmod 644 ~/.ssh/id_rsa.pub
```

## Troubleshooting Setup Issues

### Common Issues

1. **BrowserStack Authentication Failed**
   - Verify username and access key
   - Check account status and plan limits
   - Ensure no special characters in credentials

2. **Mailtrap Connection Issues**
   - Verify API token is active
   - Check inbox ID is correct
   - Ensure account has sufficient quota

3. **SSH Tunnel Issues**
   - Verify SSH key permissions
   - Check firewall settings
   - Ensure SSH service is running on target server

4. **Database Connection Issues**
   - Verify database credentials
   - Check network connectivity
   - Ensure database server allows connections

### Debug Commands

```bash
# Debug environment variables
env | grep -E "(BROWSERSTACK|MAILTRAP|SSH|DB)_"

# Test individual components
node -e "console.log('Node version:', process.version)"
npx playwright --version
npm list @playwright/test

# Check network connectivity
ping api.browserstack.com
ping mailtrap.io
```

## Next Steps

1. **Validate setup** using the validation scripts above
2. **Run a simple test** to verify everything works
3. **Review** [Execution Guide](./execution-guide.md) for test execution instructions
4. **Check** [Troubleshooting](./troubleshooting.md) if you encounter issues
