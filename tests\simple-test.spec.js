/**
 * @fileoverview Simple test to verify the test runner
 * @tags smoke example simple
 */
const { test, expect } = require('@playwright/test');

test('Verify Example Domain Title - Simple', async ({ page }) => {
  console.log('Running simple test');
  await page.goto('https://example.com');
  const title = await page.title();
  console.log(`Page title: ${title}`);
  expect(title).toBe('Example Domain');
});
