/**
 * @fileoverview DSS One-Time Purchase Flow with PayPal (TC-024)
 * @tags @regression @purchase @one_time_purchase @dss @paypal @checkout @TC024
 */
const { test: purchaseFlowFixture, expect } = require('../fixtures/workflows/purchase-flow-fixture');
const { validateTestDataEnhanced } = require('../utils/data-validator');
const { browserStackHelper } = require('../fixtures/enhanced-unified-fixture'); // Import browserStackHelper

// Extend the purchase test with PayPal specific functionality
const testWithPayPal = purchaseFlowFixture.extend({
    browserStackHelper, // Add browserStackHelper fixture
    // Add PayPal specific methods to the purchase flow
    paypalFlow: async ({ page, pageObjects, purchaseFlow }, use) => {
        const { paypalPage, checkoutPage, confirmationPage } = pageObjects;

        // Create PayPal flow helper methods
        const paypalFlow = {
            // Select PayPal and complete checkout
            selectPayPalAndCompleteCheckout: async (testData) => {
                console.log('Selecting PayPal payment method...');
                await checkoutPage.selectPaymentMethod('paypal');

                // Handle PayPal popup
                console.log('Waiting for PayPal popup...');
                const paypalLoginPromise = page.waitForEvent('popup');
                await checkoutPage.completeOrder();

                const paypalLoginPage = await paypalLoginPromise;
                console.log('PayPal popup opened, logging in...');

                // Set the page context for PayPal page
                paypalPage.page = paypalLoginPage;

                // Login to PayPal
                await paypalPage.login(
                    testData.paymentMethod.email,
                    testData.paymentMethod.password || process.env.PAYPAL_PASSWORD
                );

                // Verify payment amount
                const paymentDetails = await paypalPage.getPaymentAmount();
                console.log('PayPal payment details:', paymentDetails);

                // Validate payment amount matches expected total
                const expectedTotalAmount = testData.expectedPrice + (testData.expectedShippingCost || 2.95);
                expect(parseFloat(paymentDetails.amount)).toBeCloseTo(expectedTotalAmount, 0);
                expect(paymentDetails.currency).toContain('GBP');

                // Continue with PayPal checkout
                console.log('Continuing with PayPal checkout...');
                await paypalPage.continueToReviewOrder();

                // Wait for redirect back to our site
                console.log('Waiting for redirect back to confirmation page...');
                await confirmationPage.waitForOrderConfirmation();

                return paymentDetails;
            },

            // Verify welcome email for DSS brand
            verifyWelcomeEmail: async (testData, mailtrapHelper) => {
                const customerEmail = testData.user.email;
                console.log(`Verifying welcome email for ${customerEmail}...`);

                try {
                    const welcomeEmail = await mailtrapHelper.waitForEmail(
                        customerEmail,
                        'Welcome To Dr. Sister Skincare!',
                        60, // timeout in seconds
                        6,  // attempts
                        10  // interval in seconds
                    );

                    expect(welcomeEmail).toBeTruthy();

                    if (welcomeEmail) {
                        const welcomeEmailContent = await mailtrapHelper.getHtmlContent(
                            mailtrapHelper.inboxId,
                            welcomeEmail.id
                        );

                        expect(welcomeEmailContent).toContain('account login credentials');
                        return true;
                    }

                    return false;
                } catch (error) {
                    console.warn(`Welcome email verification failed: ${error.message}`);
                    // Don't fail the test if welcome email verification fails
                    return false;
                }
            }
        };

        await use(paypalFlow);
    }
});

testWithPayPal.describe('DSS Critical Flows', () => {
    // Store test data at the suite level
    let testData;

    testWithPayPal.beforeEach(async ({ testDataManager }) => {
        // Get values from environment variables (set by the runners)
        // Use fallbacks only if not provided by the runners
        const dataSet = process.env.TEST_DATA_SET || 'default';
        const brand = process.env.BRAND || 'dss';
        const environment = process.env.TEST_ENV || 'stage';

        console.log(`Initializing TestDataManager for brand: ${brand}, environment: ${environment}, dataSet: ${dataSet}`);

        // Allow the test runners to control these values
        // Explicitly set brand to 'dss' for this test file
        testDataManager.initialize(dataSet, 'dss', environment);

        // Adapt timeouts for mobile platforms if needed
        const isMobile = process.env.IS_MOBILE === 'true' ||
                        (process.env.PLATFORM &&
                         (process.env.PLATFORM.includes('galaxy') ||
                          process.env.PLATFORM.includes('iphone')));

        if (isMobile) {
            console.log('Mobile platform detected, extending timeouts');
            testWithPayPal.setTimeout(300000); // 5 minutes for mobile
        }

        // Initialize test data for DSS PayPal test
        testData = {
            // Use a specific product for this test
            // Get the first product available for the current brand
            product: Object.values(testDataManager.productsData)[0],
            user: testDataManager.getUser('paypal_test'),
            paymentMethod: testDataManager.getPaymentMethod('paypal_valid'),
            shippingAddressOption: 'same',
            expectedShippingMethodValue: 'domestic_tracked'
        };

        // Log test data for debugging
        console.log('Test Data:', {
            product: testData.product.name,
            user: `${testData.user.firstName} ${testData.user.lastName} (${testData.user.email})`,
            paymentMethod: testData.paymentMethod.email,
            environment: environment,
            brand: brand
        });

        // Validate test data
        validateTestDataEnhanced(testData);
    });

    testWithPayPal('TC-024: DSS One-Time Purchase - Successful Payment with PayPal', async ({
        page, pageObjects, purchaseFlow, paypalFlow, mailtrapHelper, testDataManager
    }) => {
        // Set a longer timeout for PayPal tests as they involve external redirects
        // This will be overridden by the mobile timeout if running on mobile
        testWithPayPal.setTimeout(180000);

        // 1. Navigate to product page
        await testWithPayPal.step('Navigate to product page', async () => {
            await purchaseFlow.navigateToProduct(testData);
            // Capture snapshot after navigating to the product page
            await page.waitForLoadState('networkidle');           
            await browserStackHelper.browser_snapshot(page, 'product-page-after-navigation');
        });

        // 2. Select one-time purchase
        await testWithPayPal.step('Select one-time purchase option', async () => {
            await purchaseFlow.selectPurchaseType(testData, 'oneTime');
        });

        // 3. Add to cart and checkout
        await testWithPayPal.step('Add product to cart and proceed to checkout', async () => {
            const cartDetails = await purchaseFlow.addToCartAndProceedToCheckout(testData);
            console.log('Cart details:', cartDetails);

            // Save shipping cost for later verification
            testData.expectedShippingCost = 2.95;

            // Verify cart contents
            expect(cartDetails.name).toContain(testData.product.name);
            expect(cartDetails.purchaseType).toBe('One-time Purchase');
        });

        // 4. Fill shipping information
        await testWithPayPal.step('Fill customer information', async () => {
            await purchaseFlow.fillShippingInformation(testData);
        });

        // 5. Verify shipping method
        await testWithPayPal.step('Verify shipping method', async () => {
            const shippingDetails = await purchaseFlow.verifyShippingMethodAndCost(testData);
            testData.expectedShippingCost = shippingDetails.cost;

            // Store expected total for PayPal verification
            testData.expectedTotalAmount = testData.expectedPrice + testData.expectedShippingCost;
            console.log(`Expected total amount: £${testData.expectedTotalAmount.toFixed(2)}`);
        });

        // 6. Complete checkout with PayPal
        await testWithPayPal.step('Complete checkout with PayPal', async () => {
            const paypalDetails = await paypalFlow.selectPayPalAndCompleteCheckout(testData);
            console.log('PayPal payment details:', paypalDetails);
        });

        // 7. Verify order confirmation
        await testWithPayPal.step('Verify order confirmation', async () => {
            const orderDetails = await purchaseFlow.verifyOrderConfirmation(testData);

            // Store order number for email verification
            if (orderDetails && orderDetails.orderNumber) {
                testData.orderNumber = orderDetails.orderNumber;
                testDataManager.setOrderData(
                    orderDetails.orderNumber,
                    testData.expectedTotalAmount,
                    testData.user.email
                );
            }

            // Verify order details
            expect(orderDetails.items.length).toBeGreaterThan(0);
            expect(orderDetails.items[0].name).toContain(testData.product.name);
            expect(orderDetails.items[0].purchaseType).toContain('One-time Purchase');
            expect(orderDetails.totals.total).toBeCloseTo(testData.expectedTotalAmount, 0);
        });

        // 8. Verify confirmation email
        await testWithPayPal.step('Verify order confirmation email', async () => {
            // Use the common purchase flow email verification
            const emailVerified = await purchaseFlow.verifyEmailConfirmation(testData, mailtrapHelper, {
                containsOrderNumber: true,
                timeout: 60000,
                pollingInterval: 10000
            });

            // Email verification is difficult in test environments, so don't fail the test
            console.log(`Order confirmation email verification result: ${emailVerified}`);
        });

        // 9. Verify welcome email
        await testWithPayPal.step('Verify welcome email', async () => {
            // Use the PayPal-specific welcome email verification
            const welcomeEmailVerified = await paypalFlow.verifyWelcomeEmail(testData, mailtrapHelper);
            console.log(`Welcome email verification result: ${welcomeEmailVerified}`);

            // Report test completion to BrowserStack if applicable
            if (process.env.BROWSERSTACK_SDK_ENABLED === 'true' && process.env.BROWSERSTACK_SESSION_ID) {
                console.log('Reporting test status to BrowserStack');
                try {
                    await page.evaluate(() => {
                        // This is just a placeholder. The actual marking would depend on BrowserStack's API
                        console.log('Test marked as passed in BrowserStack');
                    });
                } catch (error) {
                    console.warn('Failed to mark test status in BrowserStack:', error.message);
                }
            }
        });
    });

    testWithPayPal('Verify Account Logout Functionality', async ({ page, browserStackHelper, testDataManager }) => {
        await testWithPayPal.step('Log in to user account', async () => {
            // Navigate to login page using centralized URL management
            const baseUrl = testDataManager.getBaseUrl();
            const loginUrl = `${baseUrl}/account/login`;

            await page.goto(loginUrl);
            await page.waitForLoadState('networkidle');

            // Take screenshot of login page
            await browserStackHelper.takeScreenshot(page, 'account-login-page');

            // Get test user credentials from centralized data
            const testUser = testDataManager.getUser('default');

            // Fill login form with test credentials
            await page.fill('#CustomerEmail', testUser.email);
            await page.fill('#CustomerPassword', 'testpassword123'); // Note: Password should be in environment variables

            // Take screenshot before submitting
            await browserStackHelper.takeScreenshot(page, 'login-form-filled');

            // Submit login form
            await page.click('input[type="submit"]');

            // Wait for account page to load
            await page.waitForNavigation({ waitUntil: 'networkidle' });

            // Take screenshot of account page
            await browserStackHelper.takeScreenshot(page, 'account-page-loaded');

            // Verify logged in state
            const accountTitle = page.locator('h1');
            await expect(accountTitle).toContainText('My Account');
        });
    });
});
