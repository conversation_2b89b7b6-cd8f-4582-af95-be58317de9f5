# Test Catalog

## Overview

This document provides a comprehensive catalog of all tests in the framework, organized by category with detailed information about purpose, requirements, and execution characteristics.

## Test Categories

### 🔥 Regression Tests (`tests/regression/`)

Core business flow validation tests that ensure critical e-commerce functionality works correctly across all brands and environments.

#### TC-001: Main Purchase Flow
**File:** `tests/regression/main-purchase.spec.js`
**Tags:** `@smoke @regression @purchase @main @stage_one_time_smoke`
**Purpose:** Validates complete one-time purchase flow with credit card payment
**Brands:** AEONS (primary), DSS, YPN
**Environments:** dev, stage, prod
**Platform Support:** All platforms (desktop + mobile)
**Duration:** ~2-3 minutes
**Dependencies:** 
- Enhanced purchase flow fixture
- TestDataManager with product data
- Email verification (Mailtrap)
- Payment processing (Stripe)

**Key Test Scenarios:**
- Product page navigation and selection
- Cart functionality and checkout process
- Payment form completion with valid credit card
- Order confirmation and email verification
- Visual stability validation

#### TC-003: Sales Funnel with Upsell
**File:** `tests/regression/sales-funnel-upsell.spec.js`
**Tags:** `@regression @sales_funnel @upsell`
**Purpose:** Tests complete sales funnel flow including upsell acceptance
**Brands:** AEONS (primary)
**Environments:** stage, prod
**Platform Support:** Desktop preferred (complex admin interactions)
**Duration:** ~4-5 minutes
**Dependencies:**
- Sales funnel flow fixture
- Admin panel access
- Multiple browser contexts
- Database verification

**Key Test Scenarios:**
- Admin panel login and funnel link generation
- Customer checkout in incognito context
- Upsell presentation and acceptance
- Payment processing for multiple items
- Order completion verification

#### TC-038: Abandoned Cart Email
**File:** `tests/regression/abandoned-cart-email.spec.js`
**Tags:** `@regression @abandoned_cart @email`
**Purpose:** Validates abandoned cart email notification system
**Brands:** DSS (primary), AEONS
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~3-4 minutes
**Dependencies:**
- Enhanced unified fixture
- Admin panel access
- Sales funnel API
- Email verification (Mailtrap)

**Key Test Scenarios:**
- Cart abandonment during PayPal checkout
- API-triggered email notification
- Email content verification
- Customer recovery flow testing

#### TC-004: Subscription Purchase
**File:** `tests/regression/subscription-purchase-creditcard.spec.js`
**Tags:** `@regression @subscription @creditcard`
**Purpose:** Tests subscription-based purchase flow
**Brands:** AEONS (primary), YPN
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~3-4 minutes
**Dependencies:**
- Purchase flow fixture
- Subscription management
- Recurring payment setup

#### TC-005: PayPal One-Time Purchase
**File:** `tests/regression/one-time-purchase-paypal.spec.js`
**Tags:** `@regression @paypal @one_time`
**Purpose:** Validates PayPal payment integration
**Brands:** All brands
**Environments:** stage, prod
**Platform Support:** Desktop preferred (PayPal popup handling)
**Duration:** ~3-4 minutes
**Dependencies:**
- PayPal sandbox credentials
- Purchase flow fixture
- External payment provider integration

#### TC-006: Mixed Card Testing
**File:** `tests/regression/mixed-card.spec.js`
**Tags:** `@regression @purchase @subscription`
**Purpose:** Tests mixed cart scenarios with different purchase types and payment methods
**Brands:** AEONS (primary), DSS, YPN
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~8-10 minutes (comprehensive mixed scenarios)
**Dependencies:**
- Purchase flow fixture
- Multiple payment method configurations
- Visual analysis helpers
- Gemini AI integration

**Key Test Scenarios:**
- Mixed one-time and subscription purchases
- Multiple products in cart
- Different payment methods for different items
- Cart total calculations with mixed pricing
- Visual stability validation with AI analysis

#### TC-007: Content Verification
**File:** `tests/regression/content.spec.js`
**Tags:** `@regression @content @verification`
**Purpose:** Validates page content accuracy and consistency
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~2-3 minutes
**Dependencies:**
- Content mapping YAML files
- Text normalization rules
- Brand-specific content validation

#### TC-008: Layout Verification
**File:** `tests/regression/layout-verification.spec.js`
**Tags:** `@regression @layout @visual`
**Purpose:** Validates page layout and visual elements
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms (responsive testing)
**Duration:** ~2-3 minutes
**Dependencies:**
- Visual testing utilities
- Screenshot comparison
- Responsive design validation

#### TC-009: Responsive Design
**File:** `tests/regression/responsive.spec.js`
**Tags:** `@regression @responsive @mobile`
**Purpose:** Comprehensive responsive design testing across multiple breakpoints
**Brands:** All brands (with brand-specific product testing)
**Environments:** All environments
**Platform Support:** All platforms (especially mobile for real device validation)
**Duration:** ~10-15 minutes (multiple breakpoints and products)
**Dependencies:**
- Enhanced unified fixture
- BrowserStack helper for device simulation
- Device helper for viewport management
- Multiple viewport breakpoints testing

**Key Test Scenarios:**
- 7 different viewport breakpoints (320px to 1920px)
- Mobile-small, mobile-medium, mobile-large testing
- Tablet portrait and landscape testing
- Desktop and large desktop testing
- Product-specific responsive behavior
- Cross-brand responsive consistency

#### TC-010: Subscription Renewal
**File:** `tests/regression/subscription-renewal.spec.js`
**Tags:** `@regression @subscription @renewal`
**Purpose:** Tests automatic subscription renewal process
**Brands:** AEONS (primary), YPN
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~4-5 minutes
**Dependencies:**
- Database access (SSH tunnel)
- Subscription management API
- Payment processing validation

#### TC-011: Subscription Payment Failure
**File:** `tests/regression/subscription-renewal-payment-failure.spec.js`
**Tags:** `@regression @subscription @payment_failure`
**Purpose:** Tests subscription renewal failure handling
**Brands:** AEONS (primary), YPN
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~3-4 minutes
**Dependencies:**
- Failed payment simulation
- Customer notification testing
- Retry mechanism validation

#### TC-012: Purchase Funnel Upsell
**File:** `tests/regression/purchase-funnel-upsell.spec.js`
**Tags:** `@regression @purchase_funnel @upsell`
**Purpose:** Tests upsell functionality in purchase funnel
**Brands:** AEONS (primary)
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~4-5 minutes
**Dependencies:**
- Funnel configuration
- Upsell product management
- Multi-step checkout process

#### TC-013: Copy Verification
**File:** `tests/regression/copy-verification.spec.js`
**Tags:** `@regression @copy @content`
**Purpose:** Validates marketing copy and text content
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~2-3 minutes
**Dependencies:**
- Content mapping files
- Text comparison utilities
- Brand-specific copy validation

#### TC-014: Defect Verification
**File:** `tests/regression/defect-verification.spec.js`
**Tags:** `@regression @defect @bug_verification`
**Purpose:** Validates fixes for known defects
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~2-3 minutes
**Dependencies:**
- Defect tracking integration
- Regression testing patterns
- Bug reproduction scenarios

#### TC-015: Cancel Abandon Flow
**File:** `tests/regression/cancel-abandon.spec.js`
**Tags:** `@regression @cancel @abandon`
**Purpose:** Tests cart abandonment and cancellation flows
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~3-4 minutes
**Dependencies:**
- Session management
- Cart state tracking
- Abandonment analytics

#### TC-016: Sales Funnel Backend Complete
**File:** `tests/regression/sales-funnel-backend-complete.spec.js`
**Tags:** `@regression @sales_funnel @backend @complete`
**Purpose:** End-to-end sales funnel testing with backend verification
**Brands:** AEONS (primary)
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~5-6 minutes
**Dependencies:**
- Database access (SSH tunnel)
- Admin panel verification
- API endpoint testing
- Order processing validation

### 🛍️ Shopify Tests (`tests/shopify/`)

Brand-specific tests for Shopify platform migration and content comparison.

#### DSS Shopify Migration Tests
**Directory:** `tests/shopify/dss/`
**Purpose:** Validates DSS brand migration to Shopify platform
**Brands:** DSS only
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~10-15 minutes (comprehensive comparison)

**Test Files:**
- `content.spec.js` - Content comparison between baseline and Shopify
- `product-content.spec.js` - Product-specific content validation
- `verification/content-comparison.spec.js` - Detailed content comparison
- `verification/visual-comparison.spec.js` - Visual regression testing

**Dependencies:**
- Content mapping YAML files
- Baseline vs Shopify URL configuration
- Visual comparison utilities
- BrowserStack for cross-browser validation

#### YPN Shopify Tests
**Directory:** `tests/shopify/ypn/`
**Purpose:** YPN brand Shopify platform testing
**Brands:** YPN only
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** Variable based on test scope

#### General Shopify Product Tests
**File:** `tests/shopify/products.spec.js`
**Purpose:** Cross-brand Shopify product functionality testing
**Brands:** All brands with Shopify integration
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~5-7 minutes

### ✅ Validation Tests (`tests/validation/`)

Framework validation tests ensuring fixture compatibility and system integrity.

#### Enhanced Fixture Validation
**File:** `tests/validation/enhanced-fixture-validation.spec.js`
**Tags:** `@validation @enhanced_fixture @foundation`
**Purpose:** Validates enhanced fixture system functionality
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~2-3 minutes
**Dependencies:**
- Enhanced unified fixture
- All helper components
- TestDataManager validation

#### Purchase Flow Validation
**File:** `tests/validation/purchase-flow-basic-validation.spec.js`
**Tags:** `@validation @purchase_flow @basic`
**Purpose:** Basic purchase flow fixture validation
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~3-4 minutes

#### Purchase Flow Migration Test
**File:** `tests/validation/purchase-flow-migration-test.spec.js`
**Tags:** `@validation @migration @purchase_flow`
**Purpose:** Validates migration from old to new purchase flow fixtures
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~4-5 minutes

#### Sales Funnel Flow Validation
**File:** `tests/validation/sales-funnel-flow-validation.spec.js`
**Tags:** `@validation @sales_funnel @flow`
**Purpose:** Sales funnel fixture and workflow validation
**Brands:** AEONS (primary)
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** ~3-4 minutes

### 👁️ Visual Tests (`tests/visual/`)

Visual regression testing with AI-powered analysis.

#### Visual Regression Test
**File:** `tests/visual/visual.spec.js`
**Tags:** `@visual @regression @ai_analysis`
**Purpose:** Comprehensive visual regression testing with Gemini AI analysis
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~5-7 minutes
**Dependencies:**
- Cloudinary integration
- Gemini AI service
- Screenshot comparison utilities
- Visual analysis helpers

### 🧪 Unit Tests (`tests/unit/`)

Unit tests for framework components and utilities.

#### API Client Unit Test
**File:** `tests/unit/ApiClient.test.js`
**Purpose:** Unit testing for API client functionality and error handling
**Brands:** Framework-level (all brands)
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~1-2 minutes
**Dependencies:**
- API client modules
- Mock data for testing
- Error simulation capabilities

#### SSH Tunnel Utils Unit Test
**File:** `tests/unit/SSHTunnelUtils.test.js`
**Purpose:** Unit testing for SSH tunnel utilities and connection management
**Brands:** Framework-level (all brands)
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~1-2 minutes
**Dependencies:**
- SSH tunnel utilities
- Mock SSH connections
- Connection timeout testing

### 🔧 Utility Tests (`tests/utils/`)

Utility and helper function validation tests.

#### Page Object Verification
**File:** `tests/utils/page-object-verification.spec.js`
**Purpose:** Validates page object factory and page object integrity
**Brands:** All brands
**Environments:** All environments
**Platform Support:** All platforms
**Duration:** ~2-3 minutes
**Dependencies:**
- Page object factory
- All page object classes
- Framework validation utilities

### 📏 Magnitude Tests (`tests/magnitude/`)

Magnitude framework integration tests for advanced test orchestration.

#### AEONS Checkout Magnitude
**File:** `tests/magnitude/aeons-checkout.mag.ts`
**Purpose:** Magnitude framework integration for AEONS checkout flows
**Brand:** AEONS only
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** Variable (depends on test scope)
**Dependencies:**
- Magnitude test framework
- AEONS-specific test data
- Advanced test orchestration

#### YPN Magnitude Test Suite
**Files:**
- `tests/magnitude/ypn-authentication.mag.ts`
- `tests/magnitude/ypn-bug-verification.mag.ts`
- `tests/magnitude/ypn-cart-story.mag.ts`
- `tests/magnitude/ypn-compatibility.mag.ts`
- `tests/magnitude/ypn-products.mag.ts`

**Purpose:** Comprehensive YPN brand testing with Magnitude framework
**Brand:** YPN only
**Environments:** stage, prod
**Platform Support:** All platforms
**Duration:** Variable (5-20 minutes depending on test)
**Dependencies:**
- Magnitude test framework
- YPN-specific configurations
- Advanced workflow testing

### 📚 Example Tests (`tests/examples/`)

Demonstration tests showing framework capabilities and best practices.

#### BrowserStack Android Example
**File:** `tests/examples/bs-android-example.spec.js`
**Purpose:** Demonstrates BrowserStack Android device testing
**Platform Support:** Android devices only
**Duration:** ~2-3 minutes

#### Cloudinary Gemini Demo
**File:** `tests/examples/cloudinary-gemini-demo.spec.js`
**Purpose:** Demonstrates visual analysis with Cloudinary and Gemini AI
**Platform Support:** All platforms
**Duration:** ~3-4 minutes

#### Order Verification Example
**File:** `tests/examples/order-verification-example.spec.js`
**Purpose:** Shows order verification patterns and database integration
**Platform Support:** All platforms
**Duration:** ~3-4 minutes

#### Visual Test Example
**File:** `tests/examples/visual-test.spec.js`
**Tags:** `@smoke`
**Purpose:** Basic visual testing example with TestDataManager
**Platform Support:** All platforms
**Duration:** ~2-3 minutes

### 🧪 Unit Tests (`tests/unit/`)

Unit tests for framework components.

#### API Client Test
**File:** `tests/unit/ApiClient.test.js`
**Purpose:** Unit testing for API client functionality
**Duration:** ~1-2 minutes

#### SSH Tunnel Utils Test
**File:** `tests/unit/SSHTunnelUtils.test.js`
**Purpose:** Unit testing for SSH tunnel utilities
**Duration:** ~1-2 minutes

### 🔧 Utility Tests (`tests/utils/`)

Utility and helper function validation tests.

#### Page Object Verification
**File:** `tests/utils/page-object-verification.spec.js`
**Purpose:** Validates page object factory and page object integrity
**Duration:** ~2-3 minutes

### 📏 Magnitude Tests (`tests/magnitude/`)

Magnitude framework integration tests for advanced test orchestration.

#### AEONS Checkout Magnitude
**File:** `tests/magnitude/aeons-checkout.mag.ts`
**Purpose:** Magnitude framework integration for AEONS checkout flows
**Brand:** AEONS only
**Duration:** Variable

#### YPN Magnitude Tests
**Files:** Multiple YPN-specific magnitude tests
**Purpose:** YPN brand testing with Magnitude framework
**Brand:** YPN only
**Duration:** Variable

## Test Execution Patterns

### Tag-Based Filtering
- `@smoke` - Quick validation tests (5-10 minutes total)
- `@regression` - Comprehensive business flow tests (30-60 minutes total)
- `@visual` - Visual regression tests (10-20 minutes total)
- `@validation` - Framework validation tests (10-15 minutes total)
- `@purchase` - Purchase flow specific tests
- `@subscription` - Subscription management tests
- `@sales_funnel` - Sales funnel and upsell tests
- `@email` - Email verification tests
- `@content` - Content and copy verification tests
- `@responsive` - Responsive design tests
- `@abandoned_cart` - Cart abandonment tests

### Brand-Specific Execution
- Use `--brand=aeons|dss|ypn` to target specific brands
- Each brand has specific test data and configuration
- Some tests are brand-specific and will skip for unsupported brands
- **AEONS**: Full feature support, primary brand for new features
- **DSS**: Shopify migration focus, skincare-specific features
- **YPN**: Pet nutrition focus, subscription-heavy, Magnitude framework integration

### Environment-Specific Execution
- `dev` - Development environment testing (rapid iteration)
- `stage` - Staging environment testing (default, full feature testing)
- `prod` - Production environment testing (limited smoke tests only)

### Platform-Specific Considerations
- **Desktop Platforms**: Full feature testing, admin panel access
- **Mobile Platforms**: Real device testing preferred, touch interactions
- **BrowserStack**: Required for cross-browser and real device testing
- **Local Testing**: Faster execution, limited to available browsers

### Test Duration Guidelines
- **Quick Tests** (1-3 minutes): Basic validation, unit tests, simple flows
- **Standard Tests** (3-6 minutes): Main purchase flows, content verification
- **Complex Tests** (6-15 minutes): Sales funnels, subscription management, visual testing
- **Comprehensive Tests** (15+ minutes): Full regression suites, cross-platform testing

### Execution Recommendations

#### Daily Development Testing
```bash
# Quick smoke tests for development
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=dev --tags=@smoke
```

#### Pre-Deployment Validation
```bash
# Comprehensive regression testing
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage
node run-test.js tests/validation/ --platform=windows-chrome --brand=aeons --env=stage
```

#### Cross-Platform Validation
```bash
# Desktop and mobile testing
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@regression
node run-test.js tests/ --platform=samsung-galaxy-s23 --brand=aeons --env=stage --tags=@smoke --browserstack=true
```

#### Brand-Specific Testing
```bash
# Test all brands with core functionality
for brand in aeons dss ypn; do
  node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=$brand --env=stage
done
```

## Next Steps

See the [Execution Guide](./execution-guide.md) for detailed command examples and execution instructions.
