/**
 * Defines the structure for platform-specific capabilities.
 */
export interface PlatformCapabilities {
  os?: string;
  os_version?: string;
  browser?: string;
  browser_version?: string;
  device?: string;
  browserName?: string; // Note: 'browserName' is used for mobile, 'browser' for desktop
  realMobile?: boolean;
  'client.playwrightVersion': string;
  'browserstack.playwrightVersion': string;
}

/**
 * Defines the structure for the main test configuration.
 */
export interface TestConfig {
  testEnv: 'dev' | 'stage' | 'prod';
  brand: 'aeons' | 'dss' | 'ypn';
  platform: string;
  useBrowserStack: boolean;
  baseURL: string;
  testDir: string;
} 