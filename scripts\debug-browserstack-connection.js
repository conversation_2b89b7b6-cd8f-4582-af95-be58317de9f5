/**
 * BrowserStack Connection Debugger
 * 
 * This script tests the exact WebSocket connection that's failing
 * and provides detailed diagnostics about what's wrong.
 */

const https = require('https');
const { URL } = require('url');

// Extract credentials
const username = process.env.BROWSERSTACK_USERNAME;
const accessKey = process.env.BROWSERSTACK_ACCESS_KEY;

if (!username || !accessKey) {
  console.error('❌ Missing BrowserStack credentials');
  console.log('Set BROWSERSTACK_USERNAME and BROWSERSTACK_ACCESS_KEY environment variables');
  process.exit(1);
}

console.log('🔍 BrowserStack Connection Debugger');
console.log('====================================');

// Test 1: Check BrowserStack service status
async function checkBrowserStackStatus() {
  console.log('\n1️⃣ Checking BrowserStack service status...');
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.browserstack.com',
      path: '/automate/plan.json',
      method: 'GET',
      auth: `${username}:${accessKey}`,
      headers: {
        'User-Agent': 'playwright-debug/1.0'
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (res.statusCode === 200) {
          const plan = JSON.parse(data);
          console.log(`✅ BrowserStack API accessible`);
          console.log(`   Plan: ${plan.automate_plan || 'Unknown'}`);
          console.log(`   Parallel sessions: ${plan.parallel_sessions_max_allowed || 'Unknown'}`);
          resolve(true);
        } else {
          console.log(`❌ BrowserStack API error: ${res.statusCode}`);
          console.log(`   Response: ${data}`);
          resolve(false);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Connection error: ${error.message}`);
      resolve(false);
    });

    req.setTimeout(10000, () => {
      console.log(`❌ Request timeout`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

// Test 2: Check WebSocket endpoint construction
function analyzeWebSocketEndpoint() {
  console.log('\n2️⃣ Analyzing WebSocket endpoint construction...');
  
  const capabilities = {
    "useW3C": false,
    "browserstack.user": username,
    "browserstack.key": accessKey,
    "build": "Debug Test Run",
    "project": "Connection-Debug",
    "browserstack.debug": true,
    "browserstack.networkLogs": true,
    "browserstack.console": "debug",
    "framework": "playwright",
    "maxParallelSessions": 1,
    "browserstack.accessibility": false,
    "os_version": "13.0",
    "browserName": "chrome",
    "name": "debug-project",
    "device": "Samsung Galaxy S23",
    "realMobile": true,
    "browserstack.playwrightVersion": "1.51.0",
    "client.playwrightVersion": "1.51.0"
  };

  console.log('📋 Capabilities to be sent:');
  Object.entries(capabilities).forEach(([key, value]) => {
    console.log(`   ${key}: ${JSON.stringify(value)}`);
  });

  const capsEncoded = encodeURIComponent(JSON.stringify(capabilities));
  const wsEndpoint = `wss://cdp.browserstack.com/playwright?caps=${capsEncoded}`;
  
  console.log(`\n🔗 WebSocket endpoint length: ${wsEndpoint.length} characters`);
  
  if (wsEndpoint.length > 8192) {
    console.log('⚠️  Warning: URL might be too long for some systems');
  }
  
  // Test URL parsing
  try {
    new URL(wsEndpoint);
    console.log('✅ WebSocket URL is valid');
  } catch (error) {
    console.log(`❌ Invalid WebSocket URL: ${error.message}`);
  }

  return wsEndpoint;
}

// Test 3: Simulate the exact connection attempt
async function testWebSocketConnection(wsEndpoint) {
  console.log('\n3️⃣ Testing WebSocket connection simulation...');
  
  // Extract hostname and path from WebSocket URL
  const url = new URL(wsEndpoint);
  const hostname = url.hostname;
  const path = url.pathname + url.search;
  
  console.log(`🎯 Connecting to: ${hostname}${path.substring(0, 100)}...`);
  
  return new Promise((resolve) => {
    const options = {
      hostname: hostname,
      path: path,
      method: 'GET',
      headers: {
        'Connection': 'Upgrade',
        'Upgrade': 'websocket',
        'Sec-WebSocket-Version': '13',
        'Sec-WebSocket-Key': 'dGhlIHNhbXBsZSBub25jZQ==',
        'User-Agent': 'playwright-debug/1.0'
      }
    };

    const req = https.request(options, (res) => {
      console.log(`📡 Response status: ${res.statusCode}`);
      console.log(`📡 Response headers:`, res.headers);
      
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        if (data) {
          console.log(`📡 Response body: ${data.substring(0, 500)}...`);
        }
        resolve(res.statusCode);
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Connection error: ${error.message}`);
      resolve(null);
    });

    req.setTimeout(15000, () => {
      console.log(`❌ Connection timeout (15s)`);
      req.destroy();
      resolve(null);
    });

    req.end();
  });
}

// Test 4: Check for common issues
function checkCommonIssues() {
  console.log('\n4️⃣ Checking for common issues...');
  
  const issues = [];
  
  // Check network environment
  if (process.env.HTTP_PROXY || process.env.HTTPS_PROXY) {
    issues.push('⚠️  HTTP proxy detected - may interfere with WebSocket connections');
  }
  
  // Check firewall-related environment
  if (process.env.CORPORATE_NETWORK || process.env.COMPANY_PROXY) {
    issues.push('⚠️  Corporate network detected - firewall may block WebSocket connections');
  }
  
  // Check for VPN
  const networkInterface = require('os').networkInterfaces();
  const vpnInterfaces = Object.keys(networkInterface).filter(name => 
    name.toLowerCase().includes('vpn') || 
    name.toLowerCase().includes('tunnel') ||
    name.toLowerCase().includes('tun')
  );
  
  if (vpnInterfaces.length > 0) {
    issues.push(`⚠️  VPN interface detected: ${vpnInterfaces.join(', ')}`);
  }
  
  if (issues.length === 0) {
    console.log('✅ No obvious network issues detected');
  } else {
    issues.forEach(issue => console.log(issue));
  }
  
  return issues;
}

// Main execution
async function runDiagnostics() {
  console.log(`🔧 Running diagnostics for ${username}...`);
  
  // Run all tests
  const statusOk = await checkBrowserStackStatus();
  const wsEndpoint = analyzeWebSocketEndpoint();
  const connectionResult = await testWebSocketConnection(wsEndpoint);
  const networkIssues = checkCommonIssues();
  
  // Summary
  console.log('\n📊 Diagnostic Summary');
  console.log('====================');
  console.log(`BrowserStack API: ${statusOk ? '✅ OK' : '❌ FAILED'}`);
  console.log(`WebSocket URL: ✅ Valid`);
  console.log(`Connection Test: ${connectionResult ? (connectionResult === 101 ? '✅ OK' : `⚠️  HTTP ${connectionResult}`) : '❌ FAILED'}`);
  console.log(`Network Issues: ${networkIssues.length === 0 ? '✅ None' : `⚠️  ${networkIssues.length} detected`}`);
  
  // Recommendations
  console.log('\n💡 Recommendations');
  console.log('=================');
  
  if (!statusOk) {
    console.log('1. Check your BrowserStack credentials and account status');
    console.log('2. Verify your BrowserStack plan supports Playwright testing');
  }
  
  if (connectionResult !== 101 && connectionResult !== null) {
    console.log('3. The WebSocket endpoint is reachable but not accepting connections');
    console.log('4. This might be a temporary BrowserStack service issue');
  }
  
  if (connectionResult === null) {
    console.log('5. Network connectivity issue - check firewall/proxy settings');
    console.log('6. Try connecting from a different network');
  }
  
  if (networkIssues.length > 0) {
    console.log('7. Network environment may be interfering with WebSocket connections');
    console.log('8. Consider testing from a direct internet connection');
  }
  
  console.log('\n🔄 Next Steps:');
  console.log('1. Try running a simple test on desktop platform (windows-chrome)');
  console.log('2. Check BrowserStack status page: https://status.browserstack.com/');
  console.log('3. Contact BrowserStack support if issues persist');
}

// Error handling
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled rejection:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error);
  process.exit(1);
});

// Run diagnostics
runDiagnostics().catch(console.error); 