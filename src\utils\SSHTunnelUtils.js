/**
 * SSH Tunnel utility for database connections
 * Based on the examples in docs/Database folder
 */
const tunnel = require('tunnel-ssh');
console.log('[SSHTunnelUtils] Imported tunnel:', typeof tunnel);
console.log('[SSHTunnelUtils] Imported tunnel keys:', Object.keys(tunnel));
console.log('[SSHTunnelUtils] Result of require("tunnel-ssh"):', tunnel);
const { createTunnel } = tunnel;
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');

// Ensure environment variables are loaded
dotenv.config();

class SSHTunnelUtils {
    constructor() {
        this.tunnel = null;
        this.isConnected = false;
        this.connectionAttempts = 0;
        this.maxConnectionAttempts = 3;
    }

    /**
     * Creates an SSH tunnel to the database server
     * @param {Object} config - SSH tunnel configuration
     * @returns {Promise<Object>} - SSH tunnel
     */
    async createTunnel(config = null) {
        console.log('[SSHTunnelUtils] createTunnel called with config:', config);
        const maxAttempts = parseInt(process.env.SSH_TUNNEL_RETRY_ATTEMPTS) || 3;
        const baseDelayMs = parseInt(process.env.SSH_TUNNEL_RETRY_BASE_DELAY_MS) || 2000;
        let currentAttempt = 0;

        while (currentAttempt < maxAttempts) {
            currentAttempt++;
            try {
                // Force reload environment variables (current behavior)
                dotenv.config();
                console.log(`[SSHTunnelUtils] Creating SSH tunnel (Attempt ${currentAttempt}/${maxAttempts})...`);
                console.log(`[SSHTunnelUtils] SSH Username from env: ${process.env.SSH_USERNAME}`);

                const sshConfig = config || {
                    host: process.env.SSH_HOST,
                    port: parseInt(process.env.SSH_PORT),
                    username: process.env.SSH_USERNAME, // Corrected to use SSH_USERNAME
                    privateKey: this.getPrivateKey(), // Ensure this is robust
                    localHost: process.env.DB_HOST,
                    localPort: parseInt(process.env.DB_PORT),
                    remoteHost: process.env.DB_HOST,
                    remotePort: parseInt(process.env.DB_PORT),
// Added options for improved SSH connection reliability
                     // Equivalent to PreferredAuthentications=publickey
                     authHandler: ['publickey'],
                     // Ensure ssh-rsa is accepted for compatibility with older servers
                     algorithms: {
                         serverHostKey: ['ssh-rsa', 'ecdsa-sha2-nistp256', 'ecdsa-sha2-nistp384', 'ecdsa-sha2-nistp521']
                     },
                     // Equivalent to IdentitiesOnly=yes, prevents using keys from SSH agent
                     agent: false,
                     // Prevent keyboard-interactive authentication
                     tryKeyboard: false,
                };

                console.log(`[SSHTunnelUtils] Attempting to connect to SSH server: ${sshConfig.host}:${sshConfig.port}...`);
                console.log(`[SSHTunnelUtils] Tunneling to database: ${sshConfig.remoteHost}:${sshConfig.remotePort} via local port ${sshConfig.localPort}`);

                const tunnelConfig = { ...sshConfig, keepAlive: true };
                console.log('[SSHTunnelUtils] Tunnel configuration:', tunnelConfig);

                this.tunnel = await this.createTunnelPromise(tunnelConfig);

                console.log('[SSHTunnelUtils] SSH tunnel established successfully!');
                console.log(`[SSHTunnelUtils] Local database connection available at ${sshConfig.localHost}:${sshConfig.localPort}`);
                this.isConnected = true;
                return { host: sshConfig.localHost, port: sshConfig.localPort }; // Success, exit loop and function

            } catch (error) {
                console.error(`[SSHTunnelUtils] SSH tunnel error (Attempt ${currentAttempt}/${maxAttempts}): ${error.message}`);
                if (currentAttempt >= maxAttempts) {
                    console.error('[SSHTunnelUtils] Maximum connection attempts reached. Giving up.');
                    throw error; // Re-throw the last error
                }

                const delayMs = baseDelayMs * Math.pow(2, currentAttempt - 1);
                console.log(`[SSHTunnelUtils] Retrying connection in ${delayMs / 1000} seconds...`);
                await new Promise(resolve => setTimeout(resolve, delayMs));
            }
        }

        // Fallback throw if loop finishes without success (should not happen if maxAttempts >= 1)
        throw new Error('[SSHTunnelUtils] Failed to establish SSH tunnel after all attempts.');
    }

    /**
     * Creates a promise-based tunnel
     * @param {Object} config - Tunnel configuration
     * @returns {Promise<Object>} - SSH tunnel
     */
    async createTunnelPromise(config) {
        console.log('[SSHTunnelUtils] Value of createTunnel before call:', createTunnel);
        console.log('[SSHTunnelUtils] Calling createTunnel() with config:', config);
        let tunnelResult;
        try {
            // Await the promise returned by createTunnel
            tunnelResult = await createTunnel(config);
            console.log('[SSHTunnelUtils] Result of createTunnel call:', tunnelResult);
        } catch (e) {
            console.error('[SSHTunnelUtils] Error during createTunnel call:', e);
            throw e; // Re-throw to see the original error
        }

        console.log('[SSHTunnelUtils] Attempting to destructure tunnelResult:', tunnelResult);
        try {
            const [server, sshConnection] = tunnelResult;
            console.log('[SSHTunnelUtils] Destructuring successful. Server:', server, 'SSH Connection:', sshConnection);

            // Attach event listeners to the server object
            server.on('error', (error) => {
                console.error(`[SSHTunnelUtils] Tunnel server 'error' event: ${error.message}`);
                // Depending on desired behavior, you might want to handle this error
                // or re-throw it to be caught by the retry logic in createTunnel
            });

            server.on('ready', () => {
                console.log('[SSHTunnelUtils] Tunnel server \'ready\' event.');
            });

            server.on('close', () => {
                console.log('[SSHTunnelUtils] Tunnel server \'close\' event.');
            });

            return [server, sshConnection]; // Return the resolved array
        } catch (e) {
            console.error('[SSHTunnelUtils] Error during destructuring or event listener attachment:', e);
            throw e; // Re-throw to see the original error
        }
    }

    /**
     * Gets the private key from the environment or file
     * @returns {Buffer} - Private key buffer
     */
    getPrivateKey() {
        console.log('[SSHTunnelUtils] Attempting to get private key...');
        let privateKey = null;

        // Prioritize SSH private key from environment variable
        console.log('[SSHTunnelUtils] Checking for SSH_KEY environment variable...');
        if (process.env.SSH_KEY) { // Changed from SSH_PRIVATE_KEY to SSH_KEY
            console.log('[SSHTunnelUtils] Using SSH private key from SSH_KEY environment variable');
            const keyContent = process.env.SSH_KEY;
            if (keyContent) {
                console.log('[SSHTunnelUtils] SSH_KEY environment variable is set.');
                // Removed logging of partial private key preview for security.
                // console.log('[SSHTunnelUtils] Private key content preview (last 50 chars):', keyContent.substring(keyContent.length - 50) + '...');
                privateKey = Buffer.from(keyContent, 'utf8');
                console.log('[SSHTunnelUtils] Private key loaded from environment variable.');
            } else {
                console.error('[SSHTunnelUtils] SSH_KEY environment variable is empty.');
            }
        } else {
            console.log('[SSHTunnelUtils] SSH_KEY environment variable is not set.');
            // If not found, try to get private key from file path environment variable
            const keyPath = process.env.SSH_KEY_PATH;
            console.log('[SSHTunnelUtils] Checking for SSH_KEY_PATH environment variable...');
            if (keyPath) {
                console.log(`[SSHTunnelUtils] Attempting to read SSH private key from file: ${keyPath}`);
                try {
                    if (fs.existsSync(keyPath)) {
                        privateKey = fs.readFileSync(keyPath);
                        console.log(`[SSHTunnelUtils] Private key loaded successfully from file: ${keyPath}`);
                    } else {
                        console.error(`[SSHTunnelUtils] Error: Private key file not found at ${keyPath}.`);
                        throw new Error(`File not found.`);
                    }
                } catch (error) {
                    console.error(`[SSHTunnelUtils] Error reading SSH private key from file. Error: ${error.message}`);
                    throw new Error(`Error reading SSH private key. Error: ${error.message}`);
                }
            } else {
                console.error('[SSHTunnelUtils] Error: Neither SSH_KEY nor SSH_KEY_PATH environment variable is set.');
                throw new Error('SSH private key not found. Please set SSH_KEY or SSH_KEY_PATH environment variable.'); // Changed from SSH_PRIVATE_KEY to SSH_KEY
            }
        }

        if (!privateKey) {
            console.error('[SSHTunnelUtils] Error: Private key is null or empty after attempts.');
            throw new Error('Failed to load SSH private key.');
        }

        console.log('[SSHTunnelUtils] Private key successfully retrieved.');
        // Avoid logging the key content directly for security
        // console.log('[SSHTunnelUtils] Private key (first 50 chars):', privateKey.toString('utf8').substring(0, 50) + '...');
        return privateKey;
    }

    /**
     * Closes the SSH tunnel
     * @returns {Promise<void>}
     */
    async closeTunnel() {
        try {
            if (this.tunnel) {
                console.log('[SSHTunnelUtils] Closing SSH tunnel...');
                this.tunnel.close();
                this.tunnel = null;
                this.isConnected = false;
                console.log('[SSHTunnelUtils] SSH tunnel closed successfully');
            } else {
                console.log('[SSHTunnelUtils] No active SSH tunnel to close');
            }
        } catch (error) {
            console.error(`[SSHTunnelUtils] Error closing SSH tunnel: ${error.message}`);
            // Reset connection state even if there was an error
            this.tunnel = null;
            this.isConnected = false;
        }
    }
}

module.exports = new SSHTunnelUtils();
