// @ts-check
const { test, expect } = require('@playwright/test');

// Import the module under test
const SSHTunnelUtils = require('../../src/utils/SSHTunnelUtils');

// Since we can't easily mock modules in Playwright tests without Jest,
// we'll test the behavior more directly by overriding methods
// and using simple counters instead of complex spies.

test.describe('SSHTunnelUtils', () => {
  // Store original methods to restore after tests
  const originalConsoleLog = console.log;
  const originalConsoleError = console.error;
  const originalCreateTunnelPromise = SSHTunnelUtils.createTunnelPromise;
  const originalGetPrivateKey = SSHTunnelUtils.getPrivateKey;
  const originalSetTimeout = global.setTimeout;
  
  // Simple counters and collectors
  let consoleLogMessages;
  let consoleErrorMessages;
  let createTunnelCalls;
  
  test.beforeEach(() => {
    // Reset counters and collectors
    consoleLogMessages = [];
    consoleErrorMessages = [];
    createTunnelCalls = 0;
    
    // Mock console methods
    console.log = function(...args) {
      consoleLogMessages.push(args.join(' '));
    };
    
    console.error = function(...args) {
      consoleErrorMessages.push(args.join(' '));
    };
    
    // Mock environment variables
    process.env.SSH_TUNNEL_RETRY_ATTEMPTS = '3';
    process.env.SSH_TUNNEL_RETRY_BASE_DELAY_MS = '100';
    process.env.SSH_HOST = 'test-host';
    process.env.SSH_PORT = '22';
    process.env.SSH_USERNAME = 'test-user';
    process.env.DB_HOST = 'localhost';
    process.env.DB_PORT = '3306';
    
    // Reset SSHTunnelUtils state
    SSHTunnelUtils.tunnel = null;
    SSHTunnelUtils.isConnected = false;
    
    // Override setTimeout to execute immediately
    // @ts-ignore - We know this doesn't match the full setTimeout interface
    global.setTimeout = function(callback) {
      callback();
      return 0;
    };
  });
  
  test.afterEach(() => {
    // Restore original methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
    SSHTunnelUtils.createTunnelPromise = originalCreateTunnelPromise;
    SSHTunnelUtils.getPrivateKey = originalGetPrivateKey;
    global.setTimeout = originalSetTimeout;
  });

  test('createTunnel should retry the configured number of times on failure', async () => {
    // Arrange - Mock createTunnelPromise to always fail
    SSHTunnelUtils.createTunnelPromise = async () => {
      createTunnelCalls++;
      throw new Error('Mock tunnel error');
    };
    
    // Act & Assert
    await expect(SSHTunnelUtils.createTunnel()).rejects.toThrow('Mock tunnel error');
    
    // Verify that tunnel was attempted 3 times (as configured in env vars)
    expect(createTunnelCalls).toBe(3);
  });

  test('createTunnel should succeed after retries if tunnel eventually connects', async () => {
    // Arrange - Mock createTunnelPromise to fail on first attempt, then succeed
    SSHTunnelUtils.createTunnelPromise = async () => {
      createTunnelCalls++;
      
      if (createTunnelCalls === 1) {
        throw new Error('Mock tunnel error on first attempt');
      }
      
      // Mock server object
      const mockServer = {
        on: (event, handler) => {
          if (event === 'ready') {
            handler();
          }
          return mockServer;
        }
      };
      
      return mockServer;
    };
    
    // Act
    const result = await SSHTunnelUtils.createTunnel();
    
    // Assert
    expect(result).toEqual({
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306')
    });
    expect(createTunnelCalls).toBe(2); // Initial attempt + 1 retry
    expect(SSHTunnelUtils.isConnected).toBe(true);
  });

  test('getPrivateKey should not log private key content', async () => {
    // Arrange
    process.env.SSH_KEY = 'test-private-key-content';
    
    // Act
    try {
      SSHTunnelUtils.getPrivateKey();
    } catch (error) {
      // Ignore any errors, we're just checking logs
    }
    
    // Assert
    const allLogMessages = consoleLogMessages.join(' ') + ' ' + consoleErrorMessages.join(' ');
    
    // Verify private key content is not logged
    expect(allLogMessages.includes('test-private-key-content')).toBe(false);
    
    // Verify no partial key content is logged
    expect(allLogMessages.includes('key content preview')).toBe(false);
    expect(allLogMessages.includes('Private key (first 50 chars)')).toBe(false);
  });

  test('getPrivateKey should throw generic error without leaking paths', async () => {
    // Arrange
    delete process.env.SSH_KEY;
    process.env.SSH_KEY_PATH = '/secret/path/to/key.pem';
    
    // Mock getPrivateKey to throw an error similar to what would happen if file doesn't exist
    SSHTunnelUtils.getPrivateKey = () => {
      console.error(`[SSHTunnelUtils] Error: Private key file not found.`);
      throw new Error(`File not found.`);
    };
    
    // Act & Assert
    let errorThrown = false;
    try {
      SSHTunnelUtils.getPrivateKey();
    } catch (error) {
      errorThrown = true;
      expect(error.message).toContain('File not found');
    }
    expect(errorThrown).toBe(true);
    
    // Verify error message doesn't contain the path
    const allErrorMessages = consoleErrorMessages.join(' ');
    expect(allErrorMessages.includes('/secret/path/to/key.pem')).toBe(false);
  });

  test('closeTunnel should reset connection state even if there is an error', async () => {
    // Arrange - Create a mock tunnel that throws on close
    const mockTunnel = {
      close: () => {
        throw new Error('Error closing tunnel');
      }
    };
    
    SSHTunnelUtils.tunnel = mockTunnel;
    SSHTunnelUtils.isConnected = true;
    
    // Act
    await SSHTunnelUtils.closeTunnel();
    
    // Assert - Should reset state even if there was an error
    expect(SSHTunnelUtils.tunnel).toBeNull();
    expect(SSHTunnelUtils.isConnected).toBe(false);
  });
});