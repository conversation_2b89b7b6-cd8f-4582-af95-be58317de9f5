# Static Fallback BrowserStack Configuration
# Use this when the dynamic config generation in ci/run-env-tests.js fails

userName: ${BROWSERSTACK_USERNAME}
accessKey: ${BROWSERSTACK_ACCESS_KEY}

buildName: fallback-build
projectName: Playwright-Fallback
debug: true
networkLogs: true
consoleLogs: debug
framework: playwright
local: false
accessibility: false

parallelsPerPlatform: 1
maxParallelSessions: 2

platforms:
  - name: windows-chrome-fallback
    os: Windows
    os_version: "11"
    browser: chrome
    browser_version: latest
    browserstack.playwrightVersion: "1.51.0"
    client.playwrightVersion: "1.51.0"

  - name: samsung-galaxy-s23-fallback
    device: Samsung Galaxy S23
    os_version: "13.0"
    browserName: chrome
    realMobile: true
    browserstack.playwrightVersion: "1.51.0"
    client.playwrightVersion: "1.51.0"

  - name: iphone-14-fallback
    device: iPhone 14
    os_version: "16"
    browserName: safari
    realMobile: true
    browserstack.playwrightVersion: "1.51.0"
    client.playwrightVersion: "1.51.0"

  - name: mac-safari-fallback
    os: OS X
    os_version: Sonoma
    browser: safari
    browser_version: latest
    browserstack.playwrightVersion: "1.51.0"
    client.playwrightVersion: "1.51.0"

  - name: firefox-fallback
    os: Windows
    os_version: "11"
    browser: firefox
    browser_version: latest
    browserstack.playwrightVersion: "1.51.0"
    client.playwrightVersion: "1.51.0" 