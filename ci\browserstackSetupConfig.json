userName: i<PERSON><PERSON><PERSON><PERSON><PERSON>h_F75ojQ
accessKey: HYAZ4DUHsvFrouzKZqyj
buildName: Playwright Tests
projectName: Playwright BrowserStack
debug: true
networkLogs: true
consoleLogs: debug
parallelsPerPlatform: 1
maxParallelSessions: 1
local: false
framework: playwright
playwrightConfigOptions:
  testDir: ./tests
  timeout: 60000
  workers: 1
  retries: 1
  use:
    trace: retain-on-failure
    screenshot: only-on-failure
    video: on-first-retry
projects:
  - name: windows-chrome
    testMatch: '**/*.spec.js'
    use:
      browserName: chromium
      channel: chrome
    capabilities:
      os: Windows
      os_version: '11'
      browser: chrome
      browser_version: latest
  - name: mac-safari
    testMatch: '**/*.spec.js'
    use:
      browserName: webkit
    capabilities:
      os: OS X
      os_version: Sonoma
      browser: safari
      browser_version: latest
  - name: samsung-galaxy-s23
    testMatch: '**/*.spec.js'
    use:
      browserName: chromium
    capabilities:
      deviceName: Samsung Galaxy S23
      os_version: '13.0'
      real_mobile: true
      browserName: chrome
      browser_version: latest
  - name: iphone-14
    testMatch: '**/*.spec.js'
    use:
      browserName: webkit
    capabilities:
      deviceName: iPhone 14
      device: iPhone 14
      os_version: '16'
      real_mobile: true
      browserName: safari
      browser_version: '16.0'
      os: ios
testRunnerOptions:
  setup:
    - name: global-setup
      testMatch: '**/global-setup.js'
  teardown:
    - name: global-teardown
      testMatch: '**/global-teardown.js'
regression:
  testMatch: '**/*regression*.spec.js'
smoke:
  testMatch: '**/*smoke*.spec.js'
visual:
  testMatch: '**/*visual*.spec.js'
platforms: []
testContextOptions:
  skipSessionStatus: false
  skipSessionName: false
buildIdentifier: '#10'
browserstackLocal: false
logDir: C:\development\Malaberg\Projects\browserstack-playwright\ci\log
browserStackLocalOptions:
  skipBinaryInitialisation: false
proxySettings: {}
