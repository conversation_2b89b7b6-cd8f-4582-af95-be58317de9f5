/**
 * @fileoverview Product content verification tests using BrowserStack with AI-ready visual analysis
 */

const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { TestHelper } = require('../utils/test-helper');
const { BrowserStackConfig, PageStabilityHelper } = require('../utils/test-config');
const { VisualAnalysisHelper } = require('../utils/visual-analysis-helper');
const { BrowserStackHelper } = require('../../src/utils/browserstack/browserstack-helper');

test.describe('Product Content Verification', () => {
    let productPage;
    let browserStackConfig;
    let browserStackHelper;
    let productSlugs;

    test.beforeEach(async ({ page, testDataManager }, testInfo) => {
        // Initialize BrowserStackConfig as an instance
        browserStackConfig = new BrowserStackConfig();
        browserStackHelper = new BrowserStackHelper();
        // Use PageStabilityHelper's waitForPageStability method instead
        await PageStabilityHelper.waitForPageStability(page);
        productPage = new ProductPage(page);

        // Get all product keys from the test data manager (now available via fixture)
        productSlugs = Object.keys(testDataManager.productsData || {});
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await browserStackHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    for (const productSlug of productSlugs || []) {
        test(`Content verification - ${productSlug}`, async ({ page, sectionVerifier }) => {
            await productPage.navigateToProduct(productSlug);
            await browserStackHelper.waitForVisualStability(page);

            // Capture initial page state
            await VisualAnalysisHelper.captureScreenshot(
                page,
                'content-verification',
                `${productSlug}_initial_state`
            );

            // Verify main product sections using the new fixture
            const requiredSections = {
                'Product Title': '.product-title',
                'Product Description': '.product-description',
                'Key Ingredients': '#ingredients',
                'How to Use': '#how-to-use',
                'Reviews': '#reviews',
                'Product Benefits': '.product-benefits',
                'Money Back Guarantee': '.guarantee-section'
            };
            await sectionVerifier(page, requiredSections);

            // (Optional) Scroll each section into view for visual analysis if needed
            // for (const selector of Object.values(requiredSections)) {
            //     const element = await page.$(selector);
            //     if (element) await element.scrollIntoViewIfNeeded();
            // }
        });

        test(`SEO elements - ${productSlug}`, async ({ page }) => {
            await productPage.navigateToProduct(productSlug);
            await browserStackHelper.waitForVisualStability(page);

            // Capture initial state for SEO analysis
            await VisualAnalysisHelper.captureScreenshot(
                page,
                'seo-verification',
                `${productSlug}_seo_initial`
            );

            // Check meta tags
            const requiredMeta = [
                { name: 'description' },
                { property: 'og:title' },
                { property: 'og:description' },
                { property: 'og:image' }
            ];

            for (const meta of requiredMeta) {
                const selector = meta.name ?
                    `meta[name="${meta.name}"]` :
                    `meta[property="${meta.property}"]`;

                const element = await page.$(selector);
                expect(element, `${selector} should exist`).toBeTruthy();

                const content = await element.getAttribute('content');
                expect(content.length, `${selector} should have content`).toBeGreaterThan(0);
            }
        });

        test(`Text content quality - ${productSlug}`, async ({ page }) => {
            await productPage.navigateToProduct(productSlug);
            await browserStackHelper.waitForVisualStability(page);

            // Define content rules
            const contentRules = [
                {
                    selector: '.product-description',
                    checks: {
                        minLength: 100,
                        maxLength: 1000,
                        mustInclude: ['benefits', 'skin'],
                        cannotInclude: ['lorem ipsum', 'test']
                    }
                },
                {
                    selector: '#ingredients',
                    checks: {
                        minLength: 50,
                        mustInclude: ['ingredients', 'contains'],
                        cannotInclude: ['lorem ipsum', 'test']
                    }
                },
                {
                    selector: '#how-to-use',
                    checks: {
                        minLength: 50,
                        mustInclude: ['apply', 'use'],
                        cannotInclude: ['lorem ipsum', 'test']
                    }
                }
            ];

            for (const rule of contentRules) {
                // Scroll to section and capture for analysis
                const element = await page.$(rule.selector);
                await element.scrollIntoViewIfNeeded();
                await browserStackHelper.waitForVisualStability(page);

                await VisualAnalysisHelper.captureScreenshot(
                    page,
                    'content-quality',
                    `${productSlug}_${rule.selector.replace(/[^a-zA-Z0-9]/g, '_')}`
                );

                const content = await page.$eval(
                    rule.selector,
                    el => el.textContent.trim().toLowerCase()
                );

                // Perform content checks
                if (rule.checks.minLength) {
                    expect(content.length).toBeGreaterThanOrEqual(rule.checks.minLength);
                }
                if (rule.checks.maxLength) {
                    expect(content.length).toBeLessThanOrEqual(rule.checks.maxLength);
                }
                for (const word of rule.checks.mustInclude || []) {
                    expect(content).toContain(word.toLowerCase());
                }
                for (const word of rule.checks.cannotInclude || []) {
                    expect(content).not.toContain(word.toLowerCase());
                }
            }
        });

        test(`Form validation - ${productSlug}`, async ({ page }) => {
            await productPage.navigateToProduct(productSlug);
            await browserStackHelper.waitForVisualStability(page);

            // Define form interaction steps
            const formInteractions = [
                {
                    name: 'initial_state',
                    action: async () => {}
                },
                {
                    name: 'add_to_cart_without_options',
                    action: async () => {
                        await page.click('[data-add-to-cart]');
                    }
                },
                {
                    name: 'invalid_quantity',
                    action: async () => {
                        await page.fill('[data-quantity-input]', '999');
                    }
                }
            ];

            // Capture all form interaction states
            await VisualAnalysisHelper.captureInteractionStates(
                page,
                'form-validation',
                formInteractions
            );

            // Verify error states
            const errorMessage = await page.isVisible('.error-message');
            expect(errorMessage, 'Should show error when required options not selected').toBeTruthy();

            const quantityError = await page.isVisible('.quantity-error');
            expect(quantityError, 'Should show error for invalid quantity').toBeTruthy();
        });
    }
});
