# Playwright-BrowserStack Multi-Brand E-commerce Testing Framework

## Overview

This comprehensive testing framework provides automated testing capabilities for multi-brand e-commerce platforms using Playwright and BrowserStack integration. The framework supports three brands (AEONS, DSS, YPN) across multiple environments (dev, stage, prod) with sophisticated data management, visual testing, and email verification capabilities.

## Quick Start

### Prerequisites

1. **Node.js** (>=16.0.0)
2. **BrowserStack Account** with Automate - Desktop & Mobile plan
3. **Environment Variables** configured (see [Environment Setup](./environment-setup.md))
4. **Dependencies** installed: `npm install`

### Basic Test Execution

```bash
# Run a single test locally
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Run tests on BrowserStack
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true

# Run smoke tests for all brands
npm run test:smoke

# Run regression tests on BrowserStack
npm run test:regression:bs
```

## Framework Architecture

### Core Components

- **TestDataManager**: YAML-based centralized data management
- **Enhanced Unified Fixture**: DRY-optimized fixture system with backward compatibility
- **Page Object Factory**: Lazy-loaded page objects with caching
- **BrowserStack Service**: Session management and platform detection
- **Email Verification**: Mailtrap integration for order confirmation testing
- **Database Testing**: SSH tunneling for secure database access
- **Visual Testing**: Cloudinary + Gemini AI integration

### Supported Platforms

**Desktop:**
- `windows-chrome` - Chrome on Windows 11
- `mac-safari` - Safari on macOS Sonoma
- `firefox` - Firefox on Windows/Mac

**Mobile:**
- `samsung-galaxy-s23` - Real Samsung Galaxy S23 device
- `iphone-14` - Real iPhone 14 device

### Supported Brands

- **AEONS** (`aeons`) - Primary brand with full feature support
- **DSS** (`dss`) - Dr. Sister Skincare with Shopify migration testing
- **YPN** (`ypn`) - Your Pet Nutrition with specialized product testing

## Test Categories

### Regression Tests (`tests/regression/`)
Core business flow validation including purchase flows, subscription management, and payment processing.

### Shopify Tests (`tests/shopify/`)
Brand-specific Shopify platform migration and content comparison tests.

### Validation Tests (`tests/validation/`)
Framework validation and fixture compatibility testing.

### Visual Tests (`tests/visual/`)
Visual regression testing with AI-powered analysis.

### Examples (`tests/examples/`)
Demonstration tests showing framework capabilities and best practices.

## Key Features

### Multi-Brand Support
- Brand-specific configuration in `tests/data/brands/{brand}/`
- Environment-specific URL management
- Centralized payment method and user data management

### BrowserStack Integration
- Single concurrent session optimization
- Real device testing for mobile platforms
- Automatic session management and cleanup
- Screenshot capture with metadata

### Email Testing
- Mailtrap integration for order confirmation verification
- Brand-specific email template validation
- Automated email content verification

### Database Testing
- SSH tunneling for secure database access
- Order verification and data validation
- Subscription management testing

### Visual Testing
- Cloudinary integration for screenshot storage
- Gemini AI-powered visual analysis
- Automated visual regression detection

## Documentation Structure

- **[Test Catalog](./test-catalog.md)** - Detailed test descriptions and categorization
- **[Execution Guide](./execution-guide.md)** - Comprehensive execution instructions with examples
- **[Environment Setup](./environment-setup.md)** - Complete environment variable and secrets configuration
- **[Troubleshooting](./troubleshooting.md)** - Common issues and solutions
- **[Brand-Specific Guides](./brand-specific-guides/)** - Brand-specific testing instructions

## Getting Help

1. Check the [Troubleshooting Guide](./troubleshooting.md) for common issues
2. Review the [Test Catalog](./test-catalog.md) for test-specific information
3. Consult the [Execution Guide](./execution-guide.md) for command examples
4. Check brand-specific guides for specialized requirements

## Framework Limitations

- **BrowserStack**: Single concurrent session only (no parallel execution)
- **Mobile Testing**: Real devices required for accurate mobile testing
- **Database Access**: SSH tunneling required for database operations
- **Email Testing**: Mailtrap account required for email verification

## Next Steps

1. Review the [Environment Setup](./environment-setup.md) to configure your testing environment
2. Explore the [Test Catalog](./test-catalog.md) to understand available tests
3. Follow the [Execution Guide](./execution-guide.md) to run your first tests
4. Check brand-specific guides for specialized testing scenarios
