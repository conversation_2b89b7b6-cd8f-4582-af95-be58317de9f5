import { type MagnitudeConfig } from 'magnitude-test';

export default {
    url: "https://aeonstest.info",
    planner: {
        provider: 'openai-generic',
        options: {
            baseUrl: "https://openrouter.ai/api/v1",
            apiKey: process.env.OPENROUTER_API_KEY,
            model: "google/gemini-2.5-pro-preview-03-25"
        }
    },
    executor: {
        
        provider: 'moondream', // only moondream currently supported
        options: {
            baseUrl: 'https://moondream.ai',
            apiKey: process.env.MOONDREAM_API_KEY // not necessary if self-hosted
        }
    }

} satisfies MagnitudeConfig;
