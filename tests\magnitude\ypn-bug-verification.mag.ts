import { test } from 'magnitude-test';

/**
 * YourPetNutrition Bug Verification Tests
 * Based on the "Summary of Current Issues" section in Test_cases.md
 */

test.group('YourPetNutrition Bug Verification', () => {
    
    test('Footer language selection bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to footer section')
        .step('Check for language selection availability')
            .check('Language selection dropdown or options are present in footer')
            .check('If missing, verify this is the reported bug: Language selection is missing in the footer');

    test('Footer Shop link bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to footer and click Shop link')
        .step('Verify Shop page content')
            .check('Shop page loads without missing or empty image placeholders')
            .check('All product images are displayed correctly')
            .check('No broken image icons or empty spaces where images should be');

    test('Mobile social media icons overflow bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to footer section')
        .step('Check social media icons layout')
            .check('Social media images do not extend beyond viewport width')
            .check('Icons are properly contained within mobile screen boundaries')
            .check('No horizontal scrolling is caused by social media icons');

    test('Story page video bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Story page')
        .step('Verify video content')
            .check('Video displayed is the correct Your Pet Nutrition video')
            .check('Video is NOT the Dr Sister video (wrong video)')
            .check('Video content matches the company branding and messaging');

    test('Product video missing bugs verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Check Canine Prime product video')
            .data({ product: 'Canine Prime' })
            .check('Product video is present on Canine Prime page')
            .check('Video plays correctly when clicked')
        .step('Check Relax and Restore product video')
            .data({ product: 'Relax and Restore' })
            .check('Product video is present on Relax and Restore page')
            .check('Video plays correctly when clicked')
        .step('Check Feline 40 product video')
            .data({ product: 'Feline 40' })
            .check('Product video is present on Feline 40 page')
            .check('Video plays correctly when clicked');

    test('Canine Prime mobile ingredients visibility bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Canine Prime product page')
        .step('Check ingredients section visibility')
            .check('Vitamins and minerals image is clearly visible on mobile devices')
            .check('Image is not cut off or barely visible')
            .check('All ingredient images are properly sized for mobile viewing');

    test('Denta Soft About section image bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Denta Soft product page')
        .step('Check About Denta Soft section')
            .check('Image is present in About Denta Soft section')
            .check('Image loads correctly and is not missing')
            .check('Image is relevant to the product content');

    test('Denta Soft How to Use section text bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Denta Soft product page')
        .step('Check How to Use section weight information')
            .check('Correct weight ranges are displayed: 0>5Kg, 5>10Kg, 10>25Kg, 25+Kg')
            .check('No incorrect repetitive text like "0>5Kg on every weight type"')
            .check('Each weight range has appropriate and distinct information');

    test('Denta Soft ingredients spacing bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Denta Soft product page')
        .step('Check ingredients section formatting')
            .check('Proper spacing between "Spinach Leaf Extract" and its description')
            .check('Proper spacing between "Tapioca & Potato Starch" and its description')
            .check('All ingredient names and descriptions are properly formatted');

    test('Flexi Protect About section image bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Flexi Protect product page')
        .step('Check About Flexi Protect section')
            .check('Image is present in About Flexi Protect section')
            .check('Image loads correctly and is not missing')
            .check('Image is relevant to the product content');

    test('Flexi Protect How to Use section text bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })  
        .step('Navigate to Flexi Protect product page')
        .step('Check How to Use section weight information')
            .check('Correct weight ranges are displayed: 0>5Kg, 5>10Kg, 10>25Kg, 25+Kg')
            .check('No incorrect repetitive text like "0>5Kg on every weight type"')
            .check('Each weight range has appropriate and distinct information');

    test('Feline 40 Why section image bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Feline 40 product page')
        .step('Check Why Feline 40 section')
            .check('Image is present in Why Feline 40 section')
            .check('Image loads correctly and is not missing')
            .check('Image is relevant to the product content');

    test('Relax + Restore Cats section headers size bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Relax + Restore Cats product page')
        .step('Check section headers in "What Makes Relax+Restore work so well"')
            .check('Headers "#1 Natural Nutrition", "#2 Dual System Activation", "#3 Universally Loved Taste" are appropriately sized')
            .check('Headers are clearly visible and not too small')
            .check('Text hierarchy is properly maintained');

    test('Review sections missing bug verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Check Relax and Restore review section')
            .data({ product: 'Relax and Restore' })
            .check('Review section is present and displays correctly')
        .step('Check Denta Soft review section')
            .data({ product: 'Denta Soft' })
            .check('Review section is present and displays correctly')
        .step('Verify review functionality')
            .check('Reviews can be read and are properly formatted')
            .check('Review ratings are displayed if available');
}); 