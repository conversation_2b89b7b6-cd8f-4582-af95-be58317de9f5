/**
 * BrowserStack Configuration Validator and Debugger
 * 
 * This script validates BrowserStack configurations and capabilities
 * without interfering with the CI/CD pipeline. It helps diagnose
 * "Malformed endpoint" and other BrowserStack SDK issues.
 * 
 * Usage:
 * node scripts/validate-browserstack-config.js [platform]
 * 
 * Examples:
 * node scripts/validate-browserstack-config.js samsung-galaxy-s23
 * node scripts/validate-browserstack-config.js windows-chrome
 */

const fs = require('fs');
const path = require('path');
const yaml = require('yaml');

// Get Playwright version from package.json
const packageJson = require('../package.json');
const playwrightVersion = (packageJson.devDependencies && packageJson.devDependencies['@playwright/test']) ||
                        (packageJson.dependencies && packageJson.dependencies['@playwright/test']);

console.log('🔍 BrowserStack Configuration Validator');
console.log('========================================');

if (!playwrightVersion) {
  console.error('❌ Could not determine Playwright version from package.json');
  process.exit(1);
}

const cleanVersion = playwrightVersion.replace(/^[\^~]/, '');
console.log(`✅ Detected Playwright version: ${cleanVersion}`);

// Platform definitions (copied from ci/run-env-tests.js for consistency)
const platformCapabilities = {
  'windows-chrome': {
    os: 'Windows',
    os_version: '11',
    browser: 'chrome',
    browser_version: 'latest',
    'browserstack.playwrightVersion': cleanVersion,
    'client.playwrightVersion': cleanVersion,
  },
  'mac-safari': {
    os: 'OS X',
    os_version: 'Sonoma',
    browser: 'safari',
    browser_version: 'latest',
    'browserstack.playwrightVersion': cleanVersion,
    'client.playwrightVersion': cleanVersion,
  },
  'firefox': {
    os: 'Windows',
    os_version: '11',
    browser: 'firefox',
    browser_version: 'latest',
    'browserstack.playwrightVersion': cleanVersion,
    'client.playwrightVersion': cleanVersion,
  },
  'samsung-galaxy-s23': {
    device: 'Samsung Galaxy S23',
    os_version: '13.0',
    browserName: 'chrome',
    realMobile: true,
    'browserstack.playwrightVersion': cleanVersion,
    'client.playwrightVersion': cleanVersion,
  },
  'iphone-14': {
    device: 'iPhone 14',
    os_version: '16',
    browserName: 'safari',
    realMobile: true,
    'browserstack.playwrightVersion': cleanVersion,
    'client.playwrightVersion': cleanVersion,
  },
};

// Get platform from command line or default to samsung-galaxy-s23
const platform = process.argv[2] || 'samsung-galaxy-s23';

if (!platformCapabilities[platform]) {
  console.error(`❌ Unknown platform: ${platform}`);
  console.log('Available platforms:', Object.keys(platformCapabilities).join(', '));
  process.exit(1);
}

console.log(`🎯 Validating platform: ${platform}`);

// Check for required environment variables
console.log('\n📋 Environment Variables Check:');
const requiredEnvVars = ['BROWSERSTACK_USERNAME', 'BROWSERSTACK_ACCESS_KEY'];
let envValid = true;

requiredEnvVars.forEach(envVar => {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar}: ${process.env[envVar].substring(0, 10)}...`);
  } else {
    console.log(`❌ ${envVar}: Not set`);
    envValid = false;
  }
});

if (!envValid) {
  console.log('\n⚠️  Missing environment variables. Please set them or check your .env file.');
}

// Validate platform capabilities
console.log('\n🔧 Platform Capabilities Validation:');
const capabilities = platformCapabilities[platform];
const isMobile = capabilities.device !== undefined;

console.log(`Device Type: ${isMobile ? 'Mobile' : 'Desktop'}`);

// Check for required capability fields
const requiredFields = isMobile 
  ? ['device', 'os_version', 'browserName']
  : ['os', 'os_version', 'browser', 'browser_version'];

let capabilitiesValid = true;
requiredFields.forEach(field => {
  if (capabilities[field]) {
    console.log(`✅ ${field}: ${capabilities[field]}`);
  } else {
    console.log(`❌ ${field}: Missing`);
    capabilitiesValid = false;
  }
});

// Check version capabilities
if (capabilities['browserstack.playwrightVersion']) {
  console.log(`✅ browserstack.playwrightVersion: ${capabilities['browserstack.playwrightVersion']}`);
} else {
  console.log(`❌ browserstack.playwrightVersion: Missing`);
  capabilitiesValid = false;
}

if (capabilities['client.playwrightVersion']) {
  console.log(`✅ client.playwrightVersion: ${capabilities['client.playwrightVersion']}`);
} else {
  console.log(`❌ client.playwrightVersion: Missing`);
  capabilitiesValid = false;
}

// Generate test configuration
console.log('\n📄 Generated Configuration:');
const testConfig = {
  userName: process.env.BROWSERSTACK_USERNAME || 'YOUR_USERNAME',
  accessKey: process.env.BROWSERSTACK_ACCESS_KEY || 'YOUR_ACCESS_KEY',
  buildName: `validation-test-${new Date().getTime()}`,
  projectName: 'Validation-Test',
  debug: true,
  networkLogs: true,
  consoleLogs: 'debug',
  framework: 'playwright',
  platforms: [{
    name: 'validation-project',
    ...capabilities,
  }],
  parallelsPerPlatform: 1,
  maxParallelSessions: 1,
  local: false,
  accessibility: false,
};

console.log(JSON.stringify(testConfig, null, 2));

// Save test configuration
const configPath = path.join(__dirname, '..', 'validation-config.yml');
try {
  fs.writeFileSync(configPath, yaml.stringify(testConfig));
  console.log(`\n💾 Test configuration saved to: ${configPath}`);
} catch (error) {
  console.error(`❌ Failed to save configuration: ${error.message}`);
}

// Final validation summary
console.log('\n📊 Validation Summary:');
console.log(`Environment Variables: ${envValid ? '✅ Valid' : '❌ Invalid'}`);
console.log(`Platform Capabilities: ${capabilitiesValid ? '✅ Valid' : '❌ Invalid'}`);

if (envValid && capabilitiesValid) {
  console.log('\n🎉 Configuration looks good! You can test it with:');
  console.log(`npx browserstack-node-sdk playwright test --config=${configPath}`);
} else {
  console.log('\n⚠️  Configuration has issues. Please fix the problems above.');
}

// Additional troubleshooting tips
console.log('\n🔍 Troubleshooting Tips:');
console.log('1. Ensure BrowserStack Node SDK is up to date (npm update browserstack-node-sdk)');
console.log('2. Check BrowserStack service status at https://status.browserstack.com/');
console.log('3. Verify your BrowserStack plan supports the selected devices');
console.log('4. Try running a simple test first before complex scenarios');
console.log('5. Check network connectivity and firewall settings');

// Check BrowserStack SDK version
try {
  const sdkPackage = require('../node_modules/browserstack-node-sdk/package.json');
  console.log(`\n📦 BrowserStack Node SDK version: ${sdkPackage.version}`);
  
  // Version compatibility check
  const sdkVersion = sdkPackage.version;
  if (sdkVersion.startsWith('1.35.')) {
    console.log('✅ SDK version appears compatible with Playwright 1.51.x');
  } else {
    console.log('⚠️  SDK version compatibility unknown - consider updating');
  }
} catch (error) {
  console.log('❌ Could not determine BrowserStack SDK version');
} 