/**
 * @fileoverview Content comparison tests using BrowserStack
 */

const { test, expect } = require('@playwright/test');
const { ContentHelper } = require('../../utils/content-helper');
const { BrowserStackHelper } = require('../../../src/utils/browserstack/browserstack-helper');
const { TestDataManager } = require('../../data/test-data-manager');

// Initialize test data manager and load content mapping
const testDataManager = new TestDataManager();
testDataManager.initialize('default', 'dss', process.env.TEST_ENV || 'stage');
const {
    BASE_URLS,
    PRODUCT_URLS,
    CONTENT_SELECTORS,
    PRODUCT_CONTENT,
    TEXT_NORMALIZATION
} = testDataManager.getContentMapping();

test.describe('Content Comparison Tests', () => {
    let bsHelper;

    test.beforeEach(async ({ page }, testInfo) => {
        bsHelper = new BrowserStackHelper();
        await bsHelper.waitForVisualStability(page);
    });

    test.afterEach(async ({ }, testInfo) => {
        if (process.env.BROWSERSTACK_SESSION_ID) {
            await bsHelper.downloadArtifacts(process.env.BROWSERSTACK_SESSION_ID);
        }
    });

    for (const [productHandle, urls] of Object.entries(PRODUCT_URLS)) {
        test(`Desktop content comparison - ${productHandle}`, async ({ browser }, testInfo) => {
            const baselineContext = await browser.newContext();
            const shopifyContext = await browser.newContext();

            const baselinePage = await baselineContext.newPage();
            const shopifyPage = await shopifyContext.newPage();

            // Navigate to both versions
            await baselinePage.goto(BASE_URLS.baseline + urls.baseline);
            await shopifyPage.goto(BASE_URLS.shopify + urls.shopify);

            // Wait for content stability
            await bsHelper.waitForVisualStability(baselinePage);
            await bsHelper.waitForVisualStability(shopifyPage);

            // Take initial screenshots
            await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-initial`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'comparison'
            });
            await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-initial`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'comparison'
            });

            // Extract and compare main content sections
            const contentSections = [
                'productTitle',
                'productDescription',
                'ingredients',
                'howToUse',
                'benefits',
                'clinicalStudies'
            ];

            for (const section of contentSections) {
                const baselineText = await baselinePage.textContent(CONTENT_SELECTORS.baseline[section]);
                const shopifyText = await shopifyPage.textContent(CONTENT_SELECTORS.shopify[section]);

                const comparison = ContentHelper.compareTexts(
                    baselineText,
                    shopifyText,
                    TEXT_NORMALIZATION
                );

                // Take section screenshots for visual comparison
                await baselinePage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, CONTENT_SELECTORS.baseline[section]);
                await shopifyPage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, CONTENT_SELECTORS.shopify[section]);

                await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-${section}`, {
                    testName: testInfo.title,
                    type: 'section',
                    section
                });
                await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-${section}`, {
                    testName: testInfo.title,
                    type: 'section',
                    section
                });

                expect(comparison.identical, 
                    `Content in ${section} should match between sites`
                ).toBeTruthy();

                if (!comparison.identical) {
                    await ContentHelper.logContentDifferences(comparison.differences, {
                        title: `${productHandle} - ${section}`,
                        baselineText,
                        shopifyText
                    });
                }
            }

            // Compare structured content
            const structuredSections = {
                ingredients: {
                    selector: '.ingredients-list li',
                    comparison: 'ordered-list'
                },
                benefits: {
                    selector: '.benefits-list li',
                    comparison: 'unordered-list'
                },
                howToUse: {
                    selector: '.usage-steps li',
                    comparison: 'ordered-list'
                }
            };

            for (const [section, config] of Object.entries(structuredSections)) {
                const baselineItems = await baselinePage.$$eval(
                    CONTENT_SELECTORS.baseline[config.selector],
                    elements => elements.map(el => el.textContent.trim())
                );
                const shopifyItems = await shopifyPage.$$eval(
                    CONTENT_SELECTORS.shopify[config.selector],
                    elements => elements.map(el => el.textContent.trim())
                );

                // Take screenshots of structured content
                await baselinePage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, CONTENT_SELECTORS.baseline[config.selector]);
                await shopifyPage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, CONTENT_SELECTORS.shopify[config.selector]);

                await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-${section}-list`, {
                    testName: testInfo.title,
                    type: 'structured',
                    section
                });
                await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-${section}-list`, {
                    testName: testInfo.title,
                    type: 'structured',
                    section
                });

                if (config.comparison === 'ordered-list') {
                    expect(baselineItems).toEqual(shopifyItems);
                } else {
                    expect(new Set(baselineItems)).toEqual(new Set(shopifyItems));
                }
            }

            await baselineContext.close();
            await shopifyContext.close();
        });

        test(`Mobile content comparison - ${productHandle}`, async ({ browser }, testInfo) => {
            const deviceInfo = await bsHelper.getDeviceInfo();
            const isMobile = deviceInfo.device !== 'desktop';
            
            if (!isMobile) {
                test.skip();
                return;
            }

            const mobileViewport = { width: 360, height: 740 };
            const baselineContext = await browser.newContext({ viewport: mobileViewport });
            const shopifyContext = await browser.newContext({ viewport: mobileViewport });

            const baselinePage = await baselineContext.newPage();
            const shopifyPage = await shopifyContext.newPage();

            // Navigate and wait for stability
            await baselinePage.goto(BASE_URLS.baseline + urls.baseline);
            await shopifyPage.goto(BASE_URLS.shopify + urls.shopify);

            await bsHelper.waitForVisualStability(baselinePage);
            await bsHelper.waitForVisualStability(shopifyPage);

            // Take initial mobile screenshots
            await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-mobile-initial`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'mobile'
            });
            await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-mobile-initial`, {
                testName: testInfo.title,
                fullPage: true,
                type: 'mobile'
            });

            // Mobile-specific element checks
            const mobileElements = {
                mobileNav: {
                    baseline: '.mobile-navigation',
                    shopify: '.mobile-nav'
                },
                mobileGallery: {
                    baseline: '.mobile-product-gallery',
                    shopify: '.product-gallery--mobile'
                },
                mobileCart: {
                    baseline: '.mobile-cart-button',
                    shopify: '.cart-drawer-trigger'
                },
                stickyAddToCart: {
                    baseline: '.sticky-add-to-cart',
                    shopify: '.sticky-add-to-cart-button'
                }
            };

            // Verify mobile elements
            for (const [element, selectors] of Object.entries(mobileElements)) {
                const baselineElement = await baselinePage.$(selectors.baseline);
                const shopifyElement = await shopifyPage.$(selectors.shopify);

                expect(baselineElement).toBeTruthy();
                expect(shopifyElement).toBeTruthy();

                const baselineVisible = await baselineElement.isVisible();
                const shopifyVisible = await shopifyElement.isVisible();

                expect(baselineVisible).toBeTruthy();
                expect(shopifyVisible).toBeTruthy();

                // Take screenshots of mobile elements
                await baselinePage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, selectors.baseline);
                await shopifyPage.evaluate((sel) => {
                    document.querySelector(sel)?.scrollIntoView();
                }, selectors.shopify);

                await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-mobile-${element}`, {
                    testName: testInfo.title,
                    type: 'mobile-element',
                    element
                });
                await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-mobile-${element}`, {
                    testName: testInfo.title,
                    type: 'mobile-element',
                    element
                });
            }

            // Test mobile interactions
            await shopifyPage.click('.mobile-nav-trigger');
            await baselinePage.click('.mobile-menu-trigger');

            await bsHelper.waitForVisualStability(baselinePage);
            await bsHelper.waitForVisualStability(shopifyPage);

            await bsHelper.takeScreenshot(baselinePage, `${productHandle}-baseline-mobile-menu`, {
                testName: testInfo.title,
                type: 'mobile-interaction'
            });
            await bsHelper.takeScreenshot(shopifyPage, `${productHandle}-shopify-mobile-menu`, {
                testName: testInfo.title,
                type: 'mobile-interaction'
            });

            await baselineContext.close();
            await shopifyContext.close();
        });

        test(`SEO content comparison - ${productHandle}`, async ({ browser }) => {
            const baselineContext = await browser.newContext();
            const shopifyContext = await browser.newContext();

            const baselinePage = await baselineContext.newPage();
            const shopifyPage = await shopifyContext.newPage();

            await baselinePage.goto(BASE_URLS.baseline + urls.baseline);
            await shopifyPage.goto(BASE_URLS.shopify + urls.shopify);

            await bsHelper.waitForVisualStability(baselinePage);
            await bsHelper.waitForVisualStability(shopifyPage);

            // Compare meta tags
            const metaTags = [
                'title',
                'description',
                'keywords',
                'og:title',
                'og:description',
                'og:image'
            ];

            for (const tag of metaTags) {
                const baselineContent = await baselinePage.$eval(
                    `meta[name="${tag}"], meta[property="${tag}"]`,
                    el => el.getAttribute('content')
                );
                const shopifyContent = await shopifyPage.$eval(
                    `meta[name="${tag}"], meta[property="${tag}"]`,
                    el => el.getAttribute('content')
                );

                const comparison = ContentHelper.compareTexts(
                    baselineContent,
                    shopifyContent,
                    TEXT_NORMALIZATION
                );

                expect(comparison.identical,
                    `Meta tag ${tag} should match between sites`
                ).toBeTruthy();
            }

            await baselineContext.close();
            await shopifyContext.close();
        });
    }
});