// Fix ES Module loading issue by using dynamic import
let fetchModule;
async function getFetch() {
    if (!fetchModule) {
        try {
            // Try to use node-fetch as CommonJS module first
            fetchModule = require('node-fetch');
        } catch (e) {
            // If that fails, use dynamic import for ES module
            const module = await import('node-fetch');
            fetchModule = module.default;
        }
    }
    return fetchModule;
}

class ApiClient {
    constructor(config = {}) {
        this.baseUrl = config.baseUrl || process.env.API_BASE_URL || 'https://dstest.info';
        this.useAuth = config.useAuth || false;
        this.token = null;
        this.headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    async authenticate(username, password) {
        const endpoint = '/api/v2/admin/login_check';
        const urlString = `${this.baseUrl}${endpoint}`;
        try {
            const fetchFunc = await getFetch();
            const response = await fetchFunc(urlString, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ username, password }),
            });

            if (!response.ok) {
                let errorPayload = await response.text(); // Or response.json() if error details are JSON
                throw new Error(`Authentication failed with status ${response.status}.`);
            }

            const result = await response.json();

            if (result.token) {
                this.token = result.token;
                this.headers['Authorization'] = `Bearer ${this.token}`;
            } else {
                throw new Error(`Authentication failed: No token received from endpoint ${urlString}`);
            }
            return result;
        } catch (error) {
            console.error(`Authentication error: ${error.message}`);
            throw new Error(`Authentication encountered an error: ${error.message}.`);
        }
    }

    async get(endpoint, params = {}) {
        const urlString = `${this.baseUrl}${endpoint}`;
        try {
            if (this.useAuth && !this.token) {
                // Assuming authenticate is robust and throws on failure
                await this.authenticate(process.env.ADMIN_USERNAME, process.env.ADMIN_PASSWORD);
            }

            const fetchFunc = await getFetch();
            const url = new URL(urlString);
            Object.keys(params).forEach(key => url.searchParams.append(key, params[key]));

            const response = await fetchFunc(url, {
                method: 'GET',
                headers: this.headers,
            });

            if (!response.ok) {
                let errorPayload = await response.text(); // Or response.json()
                throw new Error(`GET request failed with status ${response.status}.`);
            }
            return await response.json();
        } catch (error) {
            console.error(`GET request error: ${error.message}`);
            throw new Error(`GET request encountered an error: ${error.message}.`);
        }
    }

    async post(endpoint, data = {}, useAuth = true) {
        const urlString = `${this.baseUrl}${endpoint}`;
        try {
            if (useAuth && this.useAuth && !this.token) {
                // Assuming authenticate is robust and throws on failure
                await this.authenticate(process.env.ADMIN_USERNAME, process.env.ADMIN_PASSWORD);
            }

            const fetchFunc = await getFetch();
            const response = await fetchFunc(urlString, {
                method: 'POST',
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                let errorPayload = await response.text(); // Or response.json()
                throw new Error(`POST request failed with status ${response.status}.`);
            }
            return await response.json();
        } catch (error) {
            console.error(`POST request error: ${error.message}`);
            throw new Error(`POST request encountered an error: ${error.message}.`);
        }
    }

    async put(endpoint, data = {}) {
        const urlString = `${this.baseUrl}${endpoint}`;
        try {
            if (this.useAuth && !this.token) {
                // Assuming authenticate is robust and throws on failure
                await this.authenticate(process.env.ADMIN_USERNAME, process.env.ADMIN_PASSWORD);
            }

            const fetchFunc = await getFetch();
            const response = await fetchFunc(urlString, {
                method: 'PUT',
                headers: {
                    ...this.headers,
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
            });

            if (!response.ok) {
                let errorPayload = await response.text(); // Or response.json()
                throw new Error(`PUT request failed with status ${response.status}.`);
            }
            return await response.json();
        } catch (error) {
            console.error(`PUT request error: ${error.message}`);
            throw new Error(`PUT request encountered an error: ${error.message}.`);
        }
    }

    async delete(endpoint) {
        const urlString = `${this.baseUrl}${endpoint}`;
        try {
            if (this.useAuth && !this.token) {
                // Assuming authenticate is robust and throws on failure
                await this.authenticate(process.env.ADMIN_USERNAME, process.env.ADMIN_PASSWORD);
            }

            const fetchFunc = await getFetch();
            const response = await fetchFunc(urlString, {
                method: 'DELETE',
                headers: this.headers,
            });

            if (response.status === 204) {
               return true; // Or some other indication of success for no content
            } else if (!response.ok) {
                let errorPayload = await response.text(); // Or response.json()
                throw new Error(`DELETE request failed with status ${response.status}.`);
            }
            return await response.json(); // If other successful statuses might return content
        } catch (error) {
            console.error(`DELETE request error: ${error.message}`);
            throw new Error(`DELETE request encountered an error: ${error.message}.`);
        }
    }
}

module.exports = ApiClient;