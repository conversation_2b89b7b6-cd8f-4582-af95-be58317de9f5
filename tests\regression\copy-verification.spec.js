/**
 * @fileoverview Aeons text content verification tests
 * @tags @regression @content @copy
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');
const { ProductPage } = require('../../src/pages/shop/ProductPage');
const { HomePage } = require('../../src/pages/general/HomePage');
const { AnalysisOrchestrator } = require('../../src/utils/gemini/service/AnalysisOrchestrator');
const { GeminiService } = require('../../src/utils/gemini/service/GeminiService');
const fs = require('fs').promises;
const path = require('path');

// Initialize test data for product URLs
let productUrls = {};

// Use the unified fixture directly - it already has BrowserStack capabilities
test.describe('Aeons Website Content Verification', () => {
    // Initialize product URLs before all tests
    test.beforeEach(async ({ testDataManager }) => {
        // Get product URLs from TestDataManager using centralized product data
        const baseUrl = testDataManager.getBaseUrl();

        // Get products from centralized data
        const totalHarmony = testDataManager.getProduct('total_harmony');
        const oliveOil = testDataManager.getProduct('ancient_roots_olive_oil');
        const boneBroth = testDataManager.getProduct('natures_gift_bone_broth');
        const sunriseSpark = testDataManager.getProduct('sunrise_spark');

        productUrls = {
            totalHarmony: `${baseUrl}${totalHarmony.urlPath}`,
            oliveOil: `${baseUrl}${oliveOil.urlPath}`,
            boneBroth: `${baseUrl}${boneBroth.urlPath}`,
            sunriseSpark: `${baseUrl}${sunriseSpark.urlPath}`,
            sunriseCategory: `${baseUrl}/ritual/sunrise`
        };

        console.log('Product URLs initialized:', productUrls);
    });

    test.describe('Text Content Verification Tests @text', () => {
        /**
         * @description Verifies that the Total Harmony product page has the correct copy for
         * the 'Inspired by 3,000 years of Ayurvedic wisdom' section.
         * @defect Asana Task #1208465337911787
         * @summary Update the copy for the section 'Inspired by 3,000 years of Ayurvedic wisdom'
         * with new text content for ingredients descriptions.
         */
        test('Verify Total Harmony updated copy', async ({ page, browserStackHelper, sectionVerifier }) => {
            await test.step('Navigate to Total Harmony product page', async () => {
                await page.goto(productUrls.totalHarmony);
                await page.waitForLoadState('networkidle');

            });

            await test.step('Verify main ingredients headline text', async () => {
                const requiredSections = {
                    'Main Ingredients Headline': 'text=Our ingredients are chosen to provide stated levels of their most active compounds',
                    'Ashwagandha Name': 'p.note:has-text("Ashwagandha")',
                    'Ashwagandha Text': 'p.note:has-text("Ashwagandha") + p.text',
                    'Saffron Name': 'p.note:has-text("Saffron")',
                    'Saffron Text': 'p.note:has-text("Saffron") + p.text',
                    'Sage Name': 'p.note:has-text("Sage")',
                    'Sage Text': 'p.note:has-text("Sage") + p.text',
                    'Maca Name': 'p.note:has-text("Maca")',
                    'Maca Text': 'p.note:has-text("Maca") + p.text',
                    'Brahmi Name': 'p.note:has-text("Brahmi")',
                    'Brahmi Text': 'p.note:has-text("Brahmi") + p.text',
                    'And More Text': 'p.note:has-text("And more")',
                };
                await sectionVerifier(page, requiredSections, { strict: false });
            });

            await test.step('Verify updated Ashwagandha description', async () => {
                const ashwagandhaText = page.locator('p.note:has-text("Ashwagandha") + p.text');
                await expect(ashwagandhaText).toHaveText('Revered for centuries for its ability to help support mental clarity and relaxation.');
            });

            await test.step('Verify Saffron description', async () => {
                const saffronText = page.locator('p.note:has-text("Saffron") + p.text');
                await expect(saffronText).toHaveText('One of the most precious herbs in the world - and revered in Ayurveda for female wellbeing');
            });

            await test.step('Verify updated Sage description', async () => {
                const sageText = page.locator('p.note:has-text("Sage") + p.text');
                await expect(sageText).toHaveText('Concentrated extract to help sweating - supporting a sense of comfort day and night');
            });

            await test.step('Verify Maca description', async () => {
                const macaText = page.locator('p.note:has-text("Maca") + p.text');
                await expect(macaText).toHaveText('Traditionally used to support a healthy libido');
            });

            await test.step('Verify updated Brahmi description', async () => {
                const brahmiText = page.locator('p.note:has-text("Brahmi") + p.text');
                await expect(brahmiText).toHaveText('A mind-assisting herb ancient monks used to assist with memorising scripture');
            });

            await test.step('Verify "And more" text', async () => {
                const andMoreText = page.locator('p.note:has-text("And more")');
                await expect(andMoreText).toBeVisible();
            });

        });

        /**
         * @description Verifies that the product titles for Olive Oil and Bone Broth have been
         * correctly updated with their full product names.
         * @defect Asana Task #1208785865992345
         * @summary Update the product names for Olive Oil and Bone Broth to include the full names
         * "Ancient Roots Olive Oil" and "Nature's Gift Bone Broth".
         */
        test('Verify updated product titles for Olive Oil and Bone Broth', async ({ page, browserStackHelper, sectionVerifier }) => {
            await test.step('Verify Ancient Roots Olive Oil title', async () => {
                await page.goto(productUrls.oliveOil);
                await page.waitForLoadState('networkidle');
                const requiredSections = {
                    'Olive Oil Title': 'h1:has-text("Ancient Roots Olive Oil")',
                };
                await sectionVerifier(page, requiredSections, { strict: false });
                const pageTitle = await page.title();
                expect(pageTitle).toContain('Ancient Roots Olive Oil');
            });

            await test.step('Verify Nature\'s Gift Bone Broth title', async () => {
                await page.goto(productUrls.boneBroth);
                await page.waitForLoadState('networkidle');
                const requiredSections = {
                    'Bone Broth Title': 'h1:has-text("Nature\'s Gift Bone Broth")',
                };
                await sectionVerifier(page, requiredSections, { strict: false });
                const pageTitle = await page.title();
                expect(pageTitle).toContain('Nature\'s Gift Bone Broth');

            });
        });

        /**
         * @description Verifies that the Sunrise product page has updated copy for ingredients information,
         * subtext description, and product benefits.
         * @defect Asana Task #1208824929067114
         * @summary Update copy for Sunrise product page including ingredient count, digestion support text,
         * and various product claims and benefits.
         */
        test('Verify Sunrise product page copy updates', async ({ page, browserStackHelper }) => {
            await test.step('Navigate to Sunrise product page', async () => {
                await page.goto(productUrls.sunriseSpark);
                await page.waitForLoadState('networkidle');
            });

            await test.step('Verify 5 powerful ingredients text', async () => {
                const ingredientsText = page.locator('text=5 powerful ingredients');
                await expect(ingredientsText).toBeVisible();
            });

            await test.step('Verify digestion support subtext', async () => {
                const supportText = page.locator('text=Digestion Support. Enriching your digestion & microbiome with Baobab, Acacia, Ginger & Calcium');
                await expect(supportText).toBeVisible();
            });

            await test.step('Verify pouch text', async () => {
                const pouchText = page.locator('text=Pouch');
                await expect(pouchText).toBeVisible();
            });

            await test.step('Verify main product benefits headline', async () => {
                const headlineText = page.locator('text=Sunrise Spark helps to enrich your digestion & microbiome and maintain digestive comfort.');
                await expect(headlineText).toBeVisible();
            });
        });

        /**
         * @description Verifies that the Sunrise category page has updated copy for the main image text,
         * body text, and product description.
         * @defect Asana Task #1208824929067129
         * @summary Update copy for "Heart of the Hadza" category page including main image text and
         * product descriptions.
         */
        test('Verify Sunrise category page copy updates', async ({ page, browserStackHelper, sectionVerifier }) => {
            await test.step('Navigate to Heart of the Hadza category page', async () => {
                await page.goto(productUrls.sunriseCategory);
                await page.waitForLoadState('networkidle');
                const requiredSections = {
                    'Sunrise Category Headline': '.quote-text',
                    'Main Image Text': '.quote-text',
                    'Bring To You Text': 'text=With Sunrise Spark we want to bring them to you.',
                    'Blend Text': 'text=Sunrise Spark blends the powerful Baobab with Acacia, Ginger & Calcium. Bringing you a powerful morning drink inspired by the Hadza\'s daily ritual.',
                    'Drink Text': 'text=Drink each morning for an invigorating spark to start your day. Each serving provides a potent kick of Ginger alongside Baobab\'s unique beneficial fibre. Setting you up to feel fantastic throughout the day.',
                    'Inspired Text': 'text=Inspired by the Hadza tribe\'s 50,000 year old morning ritual.',
                    'Combining Text': 'text=Combining Baobab with Acacia, Lemon, Ginger and Calcium to enrich your digestion and help your microbiome thrive.',
                    'Nourish Text': 'text=Nourishes a healthy microbiome',
                    'Support Text': 'text=Supports healthy digestion',
                    'Energy Text': 'text=Contributes to healthy energy production',
                };
                await sectionVerifier(page, requiredSections, { strict: false });
            });
        });
    });
});
