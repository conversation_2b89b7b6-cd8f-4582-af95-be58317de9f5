# YPN (Your Pet Nutrition) Brand Testing Guide

## Overview

YPN (Your Pet Nutrition) is a specialized pet nutrition brand with unique product characteristics and testing requirements. This guide provides specific instructions for testing YPN brand functionality, including pet-specific features, subscription management, and specialized product testing scenarios.

## Brand Configuration

### Environment URLs

```bash
# Development
YPN_DEV_URL=https://ypn-dev.info

# Staging (Default)
YPN_STAGE_URL=https://ypntest.info

# Production
YPN_PROD_URL=https://yourpetnutrition.com
```

### Admin Panel Access

```bash
# Admin panel URLs
YPN_ADMIN_DEV=https://admin.ypn-dev.info
YPN_ADMIN_STAGE=https://admin.ypntest.info
YPN_ADMIN_PROD=https://admin.yourpetnutrition.com
```

## YPN-Specific Test Data

### Product Catalog

YPN products are defined in `tests/data/brands/ypn/products.yml`:

**Primary Products:**
- `premium_dog_food` - High-quality dog nutrition
- `cat_wellness_formula` - Complete cat nutrition
- `puppy_starter_kit` - Puppy development nutrition
- `senior_dog_support` - Aging dog supplements
- `joint_health_treats` - Joint support supplements
- `digestive_health_formula` - Digestive support
- `skin_coat_supplement` - Skin and coat health
- `weight_management_food` - Weight control nutrition

**Product Features:**
- Pet type targeting (dogs, cats, puppies, seniors)
- Weight-based serving recommendations
- Age-specific formulations
- Health condition targeting
- Subscription delivery options

### Payment Methods

YPN supports standard payment methods with pet industry considerations:

```yaml
# Credit Card Testing
stripe_valid: "****************"
stripe_3dsecure: "****************"

# PayPal Testing
paypal_valid: Configured for pet product purchases
```

### User Profiles

```yaml
default:
  email: "<EMAIL>"
  firstName: "Pet"
  lastName: "Owner"
  country: "US"
  petType: "dog"        # YPN-specific field
  petWeight: "25lbs"    # YPN-specific field
  petAge: "adult"       # YPN-specific field
```

## YPN-Specific Test Execution

### Core Business Flows

#### 1. Main Purchase Flow

```bash
# Standard YPN purchase
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Mobile pet shopping experience
node run-test.js tests/regression/main-purchase.spec.js --platform=iphone-14 --brand=ypn --env=stage --browserstack=true

# With specific YPN product
PRODUCT_SLUG=premium_dog_food node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

#### 2. Subscription Management

```bash
# YPN subscription purchase (common for pet food)
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Subscription renewal testing
node run-test.js tests/regression/subscription-renewal.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Payment failure handling for subscriptions
node run-test.js tests/regression/subscription-renewal-payment-failure.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### YPN Shopify Testing

#### 1. YPN Shopify Integration

```bash
# YPN Shopify platform testing
node run-test.js tests/shopify/ypn/ --platform=windows-chrome --brand=ypn --env=stage

# Cross-platform Shopify testing
node run-test.js tests/shopify/ypn/ --platform=samsung-galaxy-s23 --brand=ypn --env=stage --browserstack=true
```

### YPN Magnitude Framework Testing

#### 1. Magnitude Test Execution

```bash
# YPN-specific Magnitude tests
npx magnitude-test tests/magnitude/ypn-products.mag.ts --brand=ypn --env=stage

# YPN authentication testing
npx magnitude-test tests/magnitude/ypn-authentication.mag.ts --brand=ypn --env=stage

# YPN cart functionality
npx magnitude-test tests/magnitude/ypn-cart-story.mag.ts --brand=ypn --env=stage

# YPN compatibility testing
npx magnitude-test tests/magnitude/ypn-compatibility.mag.ts --brand=ypn --env=stage

# YPN bug verification
npx magnitude-test tests/magnitude/ypn-bug-verification.mag.ts --brand=ypn --env=stage
```

## YPN-Specific Features Testing

### 1. Pet Type Targeting

```bash
# Test dog-specific products
PET_TYPE=dog node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Test cat-specific products
PET_TYPE=cat node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Test puppy-specific products
PET_TYPE=puppy node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### 2. Weight-Based Recommendations

```bash
# Test different pet weight categories
for weight in small medium large; do
  PET_WEIGHT=$weight node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
done
```

### 3. Age-Specific Formulations

```bash
# Test age-specific products
for age in puppy adult senior; do
  PET_AGE=$age node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
done
```

### 4. Subscription Delivery Testing

```bash
# Test subscription frequency options
for frequency in weekly monthly quarterly; do
  DELIVERY_FREQUENCY=$frequency node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=ypn --env=stage
done
```

## YPN Email Testing

### Brand-Specific Email Templates

```bash
# Test YPN order confirmation emails
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage

# YPN subscription renewal notifications
node run-test.js tests/regression/subscription-renewal.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### Email Content Verification

YPN emails include:
- Pet-specific branding and imagery
- Feeding instructions and schedules
- Pet health tips and recommendations
- Subscription management options

## YPN Visual Testing

### Brand-Specific Visual Elements

```bash
# YPN visual branding verification
node run-test.js tests/visual/visual.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Responsive design for pet product shopping
node run-test.js tests/regression/responsive.spec.js --platform=samsung-galaxy-s23 --brand=ypn --env=stage --browserstack=true
```

### Pet Product Visual Testing

```bash
# Test product image galleries
node run-test.js tests/regression/layout-verification.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Mobile product browsing experience
node run-test.js tests/regression/layout-verification.spec.js --platform=iphone-14 --brand=ypn --env=stage --browserstack=true
```

## YPN Content Testing

### Pet-Specific Content Verification

```bash
# Test YPN content accuracy
node run-test.js tests/regression/content.spec.js --platform=windows-chrome --brand=ypn --env=stage

# Copy verification for pet products
node run-test.js tests/regression/copy-verification.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### Content Mapping

YPN uses content mapping in `tests/data/brands/ypn/content-mapping.yml`:

```yaml
productContent:
  premium_dog_food:
    title: "Premium Dog Food"
    description: "Complete nutrition for adult dogs"
    ingredients: "High-quality protein, vitamins, minerals"
    feedingInstructions: "Feed according to pet weight"
```

## YPN Database Testing

### Pet-Specific Database Schema

YPN uses additional database tables:
- `pets` - Pet profile information
- `feeding_schedules` - Subscription feeding schedules
- `pet_health_records` - Health tracking data
- `veterinary_recommendations` - Vet-recommended products

### Database Test Execution

```bash
# Tests requiring YPN database access
node run-test.js tests/regression/subscription-renewal.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

## YPN-Specific Troubleshooting

### Common Issues

1. **Pet Type Configuration**
   ```bash
   # Check YPN pet type configuration
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'ypn', 'stage');
   const user = manager.getUser('default');
   console.log('Pet type:', user.petType);
   console.log('Pet weight:', user.petWeight);
   "
   ```

2. **Product Availability Issues**
   ```bash
   # Check YPN product catalog
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'ypn', 'stage');
   console.log('YPN products:', Object.keys(manager.productsData));
   "
   ```

3. **Subscription Configuration**
   ```bash
   # Verify subscription settings
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'ypn', 'stage');
   console.log('Subscription options:', manager.testData.subscription_options);
   "
   ```

## YPN Performance Considerations

### Subscription Processing Performance

```bash
# Extended timeouts for subscription tests
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=ypn --env=stage --timeout=240000

# Mobile subscription performance
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=samsung-galaxy-s23 --brand=ypn --env=stage --browserstack=true --timeout=300000
```

## YPN CI/CD Integration

### GitLab CI/CD Commands

```bash
# YPN-specific CI commands
npm run ci:test:stage:chrome -- --brand=ypn
npm run ci:test:stage:ios:bs -- --brand=ypn

# YPN Magnitude testing in CI
npx magnitude-test tests/magnitude/ypn-products.mag.ts --brand=ypn --env=stage
```

### Environment-Specific Testing

```bash
# Development environment
node run-test.js tests/ --platform=windows-chrome --brand=ypn --env=dev --tags=@smoke

# Staging environment (default)
node run-test.js tests/ --platform=windows-chrome --brand=ypn --env=stage --tags=@regression

# Production environment (limited tests)
node run-test.js tests/ --platform=windows-chrome --brand=ypn --env=prod --tags=@smoke --dataset=production
```

## YPN Test Data Management

### Pet Profile Data

```bash
# Test with different pet profiles
PET_PROFILE=small_dog node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
PET_PROFILE=large_dog node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
PET_PROFILE=indoor_cat node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### Feeding Schedule Testing

```bash
# Test feeding schedule calculations
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'ypn', 'stage');
const product = manager.getProduct('premium_dog_food');
console.log('Feeding schedule:', product.feedingSchedule);
"
```

## YPN Advanced Testing Scenarios

### Multi-Pet Household Testing

```bash
# Test multiple pet subscriptions
HOUSEHOLD_TYPE=multi_pet node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### Veterinary Integration Testing

```bash
# Test vet recommendation features
VET_INTEGRATION=true node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
```

### Health Condition Targeting

```bash
# Test health-specific products
for condition in joint_health digestive_health skin_coat; do
  HEALTH_CONDITION=$condition node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage
done
```

## Next Steps

1. **Review** [AEONS Testing Guide](./aeons-testing-guide.md) for primary brand comparison
2. **Check** [DSS Testing Guide](./dss-testing-guide.md) for Shopify migration patterns
3. **Explore** [Magnitude Framework Documentation](../test-catalog.md#magnitude-tests) for advanced testing
4. **Consult** [Troubleshooting Guide](../troubleshooting.md) for issue resolution
