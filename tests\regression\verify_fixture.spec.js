/**
 * @fileoverview test to verify the testing process
 * @tags @simple @test @smoke
 */
const { test, expect } = require('../fixtures/enhanced-unified-fixture');

test('Enhanced fixture validation test', async ({ page, testDataManager, pageObjectFactory, deviceInfo }) => {
  // Test enhanced fixture functionality
  await test.step('Validate enhanced fixture components', async () => {
    // Test testDataManager
    console.log('Testing testDataManager...');
    expect(testDataManager).toBeTruthy();
    expect(typeof testDataManager.getBaseUrl).toBe('function');

    const baseUrl = testDataManager.getBaseUrl();
    console.log(`Base URL from testDataManager: ${baseUrl}`);
    expect(baseUrl).toBeTruthy();

    // Test pageObjectFactory
    console.log('Testing pageObjectFactory...');
    expect(pageObjectFactory).toBeTruthy();
    expect(typeof pageObjectFactory.getAll).toBe('function');

    const pageObjects = pageObjectFactory.getAll();
    expect(pageObjects).toBeTruthy();
    console.log('Page objects available:', Object.keys(pageObjects));

    // Test deviceInfo
    console.log('Testing deviceInfo...');
    expect(deviceInfo).toBeTruthy();
    console.log('Device info:', deviceInfo);

    // Navigate to a real URL
    await page.goto(baseUrl);
    await page.waitForLoadState('networkidle', { timeout: 30000 });

    const title = await page.title();
    console.log(`Page title: ${title}`);
    expect(title).toBeTruthy();

    console.log('✅ Enhanced fixture validation successful!');
  });
});
