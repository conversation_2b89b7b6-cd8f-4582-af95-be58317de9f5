import { test } from 'magnitude-test';

/**
 * YourPetNutrition Authentication and Navigation Tests
 * Based on Test_cases.md sections 1 and 2
 */

test.group('YourPetNutrition Authentication and Navigation', () => {
    
    test('Store password protection verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Verify password protection page is displayed')
            .check('Password input field is visible')
            .check('Enter button is present')
        .step('Enter incorrect password')
            .data({ password: 'wrongpassword' })
            .check('Access is denied or error message is shown')
        .step('Enter correct password')
            .data({ password: '12345' })
            .check('Access is granted and homepage is displayed')
            .check('Main navigation menu is visible');

    test('Main navigation menu functionality', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate through all main menu items')
            .data('Home, Story, Shop Dogs, Shop Cats, Contact, Blog')
        .step('Click on Home link')
            .check('Homepage loads correctly with main heading visible')
        .step('Click on Story link')
            .check('Story page loads with content about the company')
        .step('Click on Shop Dogs link')
            .check('Shop Dogs page loads with dog products displayed')
        .step('Click on Shop Cats link')
            .check('Shop Cats page loads with cat products displayed')
        .step('Click on Contact link')
            .check('Contact page loads with contact information')
        .step('Click on Blog link')
            .check('Blog page loads correctly');

    test('Footer navigation verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Scroll to footer section')
        .step('Verify footer links are present')
            .check('Shop link is visible in footer')
            .check('Contact link is visible in footer')
            .check('Shipping Policy link is visible in footer')
            .check('Returns & Refunds link is visible in footer')
            .check('Privacy Policy link is visible in footer')
            .check('Terms of Use link is visible in footer')
        .step('Click on Shop link in footer')
            .check('Shop page loads with product images displayed correctly')
            .check('No missing or empty image placeholders are visible')
        .step('Verify social media links')
            .check('Facebook link is present')
            .check('Instagram link is present')
            .check('Twitter link is present');

    test('Language selection verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Scroll to footer section')
        .step('Check for language selection options')
            .check('Language selection dropdown or links are available')
        .step('If language selection exists, test functionality')
            .data({ language: 'Deutsch' })
            .check('Selected language is applied and displayed correctly');

    test('Responsive design verification - Desktop', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Verify desktop layout')
            .check('Header is properly aligned and visible')
            .check('Main content is properly displayed')
            .check('Navigation menu is horizontal and accessible')
            .check('Footer is properly positioned')
            .check('All elements fit within viewport without horizontal scrolling');

    test('Responsive design verification - Mobile', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password on mobile viewport')
            .data({ 
                password: '12345',
                viewport: 'mobile'
            })
        .step('Verify mobile layout adjustments')
            .check('Mobile menu is accessible')
            .check('Content stacks vertically appropriately')
            .check('Social media images do not extend beyond viewport width')
            .check('All text is readable without horizontal scrolling')
            .check('Touch targets are appropriately sized');
}); 