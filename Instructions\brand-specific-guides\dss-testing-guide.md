# DSS (Dr. <PERSON>) Brand Testing Guide

## Overview

DSS (Dr. <PERSON> Skincare) is a specialized skincare brand with unique Shopify migration requirements and content comparison testing. This guide provides specific instructions for testing DSS brand functionality, including Shopify platform migration, content verification, and brand-specific features.

## Brand Configuration

### Environment URLs

```bash
# Development
DSS_DEV_URL=https://dss-dev.info

# Staging (Default)
DSS_STAGE_URL=https://dss.crm-test.info

# Production
DSS_PROD_URL=https://drsisterskincare.com

# Shopify Migration URL
DSS_SHOPIFY_URL=https://dss-shopify.example.com
```

### Admin Panel Access

```bash
# Admin panel URLs
DSS_ADMIN_DEV=https://admin.dss-dev.info
DSS_ADMIN_STAGE=https://admin.dss.crm-test.info
DSS_ADMIN_PROD=https://admin.drsisterskincare.com
```

## DSS-Specific Test Data

### Product Catalog

DSS products are defined in `tests/data/brands/dss/products.yml`:

**Primary Products:**
- `dark_spot_vanish` - Signature dark spot treatment
- `age_defying_serum` - Anti-aging serum
- `hydrating_cleanser` - Daily cleanser
- `brightening_mask` - Weekly treatment mask
- `vitamin_c_serum` - Antioxidant serum
- `retinol_cream` - Night treatment
- `sunscreen_spf50` - Daily protection

**Product Features:**
- Skincare-specific product attributes
- Size variations (30ml, 50ml, 100ml)
- Skin type targeting (oily, dry, combination, sensitive)
- Treatment duration options

### Payment Methods

DSS supports standard payment methods with skincare industry considerations:

```yaml
# Credit Card Testing
stripe_valid: "****************"
stripe_3dsecure: "****************"

# PayPal Testing
paypal_valid: Configured for skincare purchases
```

### User Profiles

```yaml
default:
  email: "<EMAIL>"
  firstName: "Sarah"
  lastName: "Beauty"
  country: "US"
  skinType: "combination"  # DSS-specific field
```

## DSS-Specific Test Execution

### Core Business Flows

#### 1. Main Purchase Flow

```bash
# Standard DSS purchase
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage

# Mobile skincare shopping experience
node run-test.js tests/regression/main-purchase.spec.js --platform=iphone-14 --brand=dss --env=stage --browserstack=true

# With specific DSS product
PRODUCT_SLUG=dark_spot_vanish node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage
```

#### 2. Abandoned Cart Email Testing

```bash
# DSS-specific abandoned cart flow
node run-test.js tests/regression/abandoned-cart-email.spec.js --platform=windows-chrome --brand=dss --env=stage

# Test with DSS product in cart
node run-test.js tests/regression/abandoned-cart-email.spec.js --platform=windows-chrome --brand=dss --env=stage
```

### DSS Shopify Migration Testing

#### 1. Content Comparison Tests

```bash
# Complete DSS Shopify migration test suite
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage

# Specific content comparison
node run-test.js tests/shopify/dss/content.spec.js --platform=windows-chrome --brand=dss --env=stage

# Product-specific content validation
node run-test.js tests/shopify/dss/product-content.spec.js --platform=windows-chrome --brand=dss --env=stage
```

#### 2. Visual Comparison Testing

```bash
# Visual regression between baseline and Shopify
node run-test.js tests/shopify/dss/verification/visual-comparison.spec.js --platform=windows-chrome --brand=dss --env=stage

# Content comparison verification
node run-test.js tests/shopify/dss/verification/content-comparison.spec.js --platform=windows-chrome --brand=dss --env=stage
```

#### 3. Cross-Platform Shopify Testing

```bash
# Desktop Shopify testing
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage

# Mobile Shopify testing
node run-test.js tests/shopify/dss/ --platform=samsung-galaxy-s23 --brand=dss --env=stage --browserstack=true

# Safari-specific testing (important for Shopify)
node run-test.js tests/shopify/dss/ --platform=mac-safari --brand=dss --env=stage --browserstack=true
```

## DSS Content Mapping

### Content Comparison Configuration

DSS uses content mapping files in `tests/data/brands/dss/content-mapping.yml`:

```yaml
baseUrls:
  baseline: "https://dss.crm-test.info"
  shopify: "https://dss-shopify.example.com"

productUrls:
  dark_spot_vanish:
    baseline: "/products/dark-spot-vanish"
    shopify: "/products/dark-spot-vanish-serum"

contentSelectors:
  productTitle: ".product-title, .product__title"
  productPrice: ".price, .product__price"
  productDescription: ".product-description, .product__description"
```

### Content Verification Testing

```bash
# Verify content mapping configuration
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'dss', 'stage');
const mapping = manager.getContentMapping();
console.log('DSS content mapping:', JSON.stringify(mapping, null, 2));
"

# Test content comparison
node run-test.js tests/shopify/dss/content.spec.js --platform=windows-chrome --brand=dss --env=stage
```

## DSS Email Testing

### Brand-Specific Email Templates

```bash
# Test DSS order confirmation emails
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage

# DSS abandoned cart email testing
node run-test.js tests/regression/abandoned-cart-email.spec.js --platform=windows-chrome --brand=dss --env=stage
```

### Email Content Verification

DSS emails include:
- Skincare-specific branding
- Product care instructions
- Skin type recommendations
- Treatment schedules

## DSS Visual Testing

### Brand-Specific Visual Elements

```bash
# DSS visual branding verification
node run-test.js tests/visual/visual.spec.js --platform=windows-chrome --brand=dss --env=stage

# Responsive design for skincare products
node run-test.js tests/regression/responsive.spec.js --platform=iphone-14 --brand=dss --env=stage --browserstack=true
```

### Shopify Visual Regression

```bash
# Compare baseline vs Shopify visuals
node run-test.js tests/shopify/dss/verification/visual-comparison.spec.js --platform=windows-chrome --brand=dss --env=stage

# Mobile visual comparison
node run-test.js tests/shopify/dss/verification/visual-comparison.spec.js --platform=samsung-galaxy-s23 --brand=dss --env=stage --browserstack=true
```

## DSS-Specific Features Testing

### 1. Skin Type Targeting

```bash
# Test skin type-specific product recommendations
SKIN_TYPE=oily node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage
SKIN_TYPE=dry node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage
```

### 2. Product Size Variations

```bash
# Test different product sizes
for size in 30ml 50ml 100ml; do
  PRODUCT_SIZE=$size node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage
done
```

### 3. Treatment Duration Options

```bash
# Test treatment duration selection
TREATMENT_DURATION=30days node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage
```

## DSS Shopify Migration Validation

### Pre-Migration Testing

```bash
# Baseline functionality testing
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage

# Content verification on baseline
node run-test.js tests/regression/content.spec.js --platform=windows-chrome --brand=dss --env=stage
```

### Post-Migration Testing

```bash
# Complete Shopify migration validation
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage

# Cross-browser Shopify testing
node run-test.js tests/shopify/dss/ --platform=mac-safari --brand=dss --env=stage --browserstack=true
node run-test.js tests/shopify/dss/ --platform=firefox --brand=dss --env=stage
```

### Migration Comparison Reports

```bash
# Generate migration comparison report
node run-test.js tests/shopify/dss/verification/content-comparison.spec.js --platform=windows-chrome --brand=dss --env=stage

# Visual comparison report
node run-test.js tests/shopify/dss/verification/visual-comparison.spec.js --platform=windows-chrome --brand=dss --env=stage
```

## DSS-Specific Troubleshooting

### Common Issues

1. **Shopify URL Configuration**
   ```bash
   # Verify Shopify URL configuration
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'dss', 'stage');
   console.log('DSS Base URL:', manager.getBaseUrl());
   "
   ```

2. **Content Mapping Issues**
   ```bash
   # Check content mapping file
   cat tests/data/brands/dss/content-mapping.yml
   
   # Validate YAML syntax
   node -e "
   const yaml = require('js-yaml');
   const fs = require('fs');
   const content = fs.readFileSync('tests/data/brands/dss/content-mapping.yml', 'utf8');
   console.log('Content mapping valid:', !!yaml.load(content));
   "
   ```

3. **Product-Specific Issues**
   ```bash
   # Check DSS product configuration
   node -e "
   const TestDataManager = require('./tests/data/test-data-manager');
   const manager = new TestDataManager();
   manager.initialize('default', 'dss', 'stage');
   console.log('DSS products:', Object.keys(manager.productsData));
   "
   ```

## DSS Performance Considerations

### Shopify Platform Performance

```bash
# Extended timeouts for Shopify testing
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage --timeout=180000

# Mobile performance testing
node run-test.js tests/shopify/dss/ --platform=samsung-galaxy-s23 --brand=dss --env=stage --browserstack=true --timeout=300000
```

## DSS CI/CD Integration

### GitLab CI/CD Commands

```bash
# DSS-specific CI commands
npm run ci:test:stage:chrome -- --brand=dss
npm run ci:test:stage:safari:bs -- --brand=dss

# DSS Shopify migration testing
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage
```

### Environment-Specific Testing

```bash
# Development environment
node run-test.js tests/ --platform=windows-chrome --brand=dss --env=dev --tags=@smoke

# Staging environment (default)
node run-test.js tests/ --platform=windows-chrome --brand=dss --env=stage --tags=@regression

# Production environment (limited tests)
node run-test.js tests/ --platform=windows-chrome --brand=dss --env=prod --tags=@smoke --dataset=production
```

## DSS Test Data Management

### Environment-Specific Overrides

DSS supports environment-specific data overrides:

```bash
# Check available DSS data sets
ls tests/data/brands/dss/datasets/

# Use staging-specific data
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage --dataset=staging
```

### Product Data Validation

```bash
# Validate DSS product data
node -e "
const { validateProductData } = require('./tests/utils/data-validator');
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'dss', 'stage');
const product = manager.getProduct('dark_spot_vanish');
validateProductData(product);
console.log('✅ DSS product data validation passed');
"
```

## Next Steps

1. **Review** [AEONS Testing Guide](./aeons-testing-guide.md) for comparison with primary brand
2. **Check** [YPN Testing Guide](./ypn-testing-guide.md) for other brand patterns
3. **Consult** [Shopify Migration Documentation](../test-catalog.md#shopify-tests) for detailed migration testing
4. **Follow** [Troubleshooting Guide](../troubleshooting.md) for issue resolution
