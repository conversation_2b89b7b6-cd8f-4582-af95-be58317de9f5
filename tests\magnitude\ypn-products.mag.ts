import { test } from 'magnitude-test';

/**
 * YourPetNutrition Product Tests
 * Based on Test_cases.md sections 4-10
 */

test.group('YourPetNutrition Product Functionality', () => {
    
    test('Dog products display verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Dogs page')
        .step('Verify all dog products are displayed')
            .check('Canine Prime product is visible with image and price')
            .check('Relax + Restore product is visible with image and price')
            .check('Denta Soft product is visible with image and price')
            .check('Flexi Protect product is visible with image and price')
        .step('Verify product information completeness')
            .check('Each product shows correct name, image, price, and description')
            .check('No missing product images or placeholders');

    test('Cat products display verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Cats page')
        .step('Verify all cat products are displayed')
            .check('Feline 40 product is visible with image and price')
            .check('Relax + Restore Cats product is visible with image and price')
        .step('Verify product information completeness')
            .check('Each product shows correct name, image, price, and description')
            .check('No missing product images or placeholders');

    test('Canine Prime product detail page verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Dogs page and click Canine Prime')
        .step('Verify product detail page elements')
            .check('Product title "Canine Prime" is displayed')
            .check('Product images are visible and load correctly')
            .check('Product price is displayed clearly')
            .check('Product description is complete and informative')
        .step('Verify purchase options')
            .check('ONE TIME PURCHASE option is available')
            .check('SUBSCRIBE AND SAVE option is available')
            .check('Radio buttons show selected state when clicked')
            .check('No duplicate text appears in radio button labels')
        .step('Verify quantity selection')
            .check('1 jar option is available')
            .check('3 jars option is available with discount information')
            .check('6 jars option is available with discount information')
            .check('Orange frame appears on hover for quantity options')
            .check('Price updates correctly when different quantities are selected')
        .step('Check for product video')
            .check('Product video is present and plays correctly')
        .step('Verify ingredients section on mobile')
            .data({ viewport: 'mobile' })
            .check('Vitamins and minerals image is clearly visible on mobile devices');

    test('Denta Soft product detail page verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Dogs page and click Denta Soft')
        .step('Verify product detail page elements')
            .check('Product title "Denta Soft" is displayed')
            .check('Product images are visible and load correctly')
            .check('Product price is displayed clearly')
        .step('Verify About Denta Soft section')
            .check('About Denta Soft section image is displayed correctly')
        .step('Verify How to Use section')
            .check('Correct weight ranges are displayed: 0>5Kg, 5>10Kg, 10>25Kg, 25+Kg')
            .check('No incorrect text like "0>5Kg on every weight type"')
        .step('Verify ingredients section spacing')
            .check('Proper spacing between "Spinach Leaf Extract" and its description')
            .check('Proper spacing between "Tapioca & Potato Starch" and its description')
        .step('Verify review section presence')
            .check('Review section is present and displays correctly')
        .step('Check for product video')
            .check('Product video is present and plays correctly');

    test('Flexi Protect product detail page verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Dogs page and click Flexi Protect')
        .step('Verify product detail page elements')
            .check('Product title "Flexi Protect" is displayed')
            .check('Product images are visible and load correctly')
        .step('Verify About Flexi Protect section')
            .check('About Flexi Protect section image is displayed correctly')
        .step('Verify How to Use section')
            .check('Correct weight ranges are displayed: 0>5Kg, 5>10Kg, 10>25Kg, 25+Kg')
            .check('No incorrect text like "0>5Kg on every weight type"')
        .step('Verify What\'s Inside section')
            .check('What\'s Inside Flexi Protect section is present and displays correctly')
        .step('Verify FAQ active ingredients')
            .check('What are the active ingredients question has complete answer content')
            .check('Active ingredients information is displayed properly in FAQ');

    test('Feline 40 product detail page verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Cats page and click Feline 40')
        .step('Verify product detail page elements')
            .check('Product description is complete and not truncated')
        .step('Verify Why Feline 40 section')
            .check('Why Feline 40 section image is displayed correctly')
        .step('Check for product video')
            .check('Product video is present and plays correctly')
        .step('Verify Mushroom Blend text alignment')
            .check('Mushroom Blend text does not overlap with the mushroom image')
            .check('Text and image are properly aligned in Ingredients section');

    test('Relax + Restore Cats product detail page verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Cats page and click Relax + Restore Cats')
        .step('Verify product detail page elements')
            .check('Product title "Relax + Restore Cats" is displayed')
            .check('Product images are visible and load correctly')
        .step('Verify section headers sizing')
            .check('Headers "#1 Natural Nutrition", "#2 Dual System Activation", "#3 Universally Loved Taste" are appropriately sized and visible')
        .step('Verify key ingredients formatting')
            .check('Proper spacing between ingredient names and descriptions')
            .check('L-Tryptophan description is properly formatted')
            .check('Passiflora Incarnata description is properly formatted')
            .check('No backslash formatting issues are present');

    test('Relax and Restore Dogs product verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Access the website with correct password')
            .data({ password: '12345' })
        .step('Navigate to Shop Dogs page and click Relax and Restore')
        .step('Verify product detail page elements')
            .check('Product title "Relax and Restore" is displayed')
            .check('Product images are visible and load correctly')
        .step('Verify purchase options')
            .check('ONE TIME PURCHASE option is available')
            .check('SUBSCRIBE AND SAVE option is available')
            .check('Radio buttons show selected state when clicked')
        .step('Verify subscription frequency dropdown')
            .check('Frequency dropdown appears when SUBSCRIBE AND SAVE is selected')
            .check('Months are in correct order with 1 month first')
            .check('1 Month is selected by default')
        .step('Verify review section presence')
            .check('Review section is present and displays correctly')
        .step('Check for product video')
            .check('Product video is present and plays correctly');
}); 