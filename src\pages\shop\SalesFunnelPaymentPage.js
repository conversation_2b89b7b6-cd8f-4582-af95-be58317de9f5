/**
 * Page object for the sales funnel payment page
 */
class SalesFunnelPaymentPage {
    /**
     * @param {import('@playwright/test').Page} page
     */
    constructor(page) {
        this.page = page;
        this.selectors = {
            // Order summary
            orderSummary: {
                container: '.order-summary, .ch-price-breakdown',
                lineItems: '.line-item, .checkout-order-summary-item',
                lineItemTitle: '.line-title, .item-name',
                lineItemPrice: '.price-container div, .item-price',
                subtotal: '.ch-subtotal-value span, .order-summary-subtotal',
                shippingCost: '.ch-shipping-value span, .order-summary-shipping',
                total: '.ch-total-price-value span, .order-summary-total'
            },
            
            // Shipping section
            shipping: {
                section: '.shipping-section, .checkout-shipping',
                methodContainer: '.shipping-methods, .checkout-shipping-methods',
                methods: 'input[name="app_one_page_checkout[shipments][0][method]"]',
                methodLabel: (value) => `label:has(input[value="${value}"])`,
                continueButton: '.checkout-v3-continue-to-payment, .continue-to-payment'
            },
            
            // Payment section
            payment: {
                section: '.payment-section, .checkout-payment',
                methods: {
                    creditCardOption: 'label:has([data-payment-method-container="new-card"])',
                    paypalOption: 'label:has([data-payment-method-container="paypal"])',
                    klarnaOption: 'label:has([data-payment-method-container="klarna_checkout"])'
                },
                creditCard: {
                    numberFrame: 'iframe[title="Secure card number input frame"]',
                    expiryFrame: 'iframe[title="Secure expiration date input frame"]',
                    cvcFrame: 'iframe[title="Secure CVC input frame"]',
                    numberInput: '[name="cardnumber"]',
                    expiryInput: '[name="exp-date"]',
                    cvcInput: '[name="cvc"]'
                },
                completeOrderButton: 'button[form="app_one_page_checkout"], .checkout-v3-payment-submit-button button'
            }
        };
    }

    /**
     * Wait for the payment page to load
     */
    async waitForPageLoad() {
        console.log('Waiting for payment page to load');
        try {
            await this.page.waitForURL('**/checkout/finalize**', { timeout: 30000 });
            
            // Check if we have order summary
            await this.page.waitForSelector(this.selectors.orderSummary.container, { 
                state: 'visible', 
                timeout: 10000 
            });
            
            // Take a screenshot for debugging
            await this.page.screenshot({ path: `sales-funnel-payment-${Date.now()}.png` });
            
            console.log('Payment page loaded successfully');
        } catch (error) {
            console.error(`Error waiting for payment page: ${error.message}`);
            throw new Error(`Failed to load payment page: ${error.message}`);
        }
    }

    /**
     * Get the order summary details
     * @returns {Promise<{items: Array<{name: string, price: string}>, subtotal: string, shipping: string, total: string}>}
     */
    async getOrderSummary() {
        console.log('Getting order summary');
        
        const summary = {
            items: [],
            subtotal: '',
            shipping: '',
            total: ''
        };
        
        try {
            // Get all line items
            const lineItems = this.page.locator(this.selectors.orderSummary.lineItems);
            const count = await lineItems.count();
            
            for (let i = 0; i < count; i++) {
                const item = lineItems.nth(i);
                const titleEl = item.locator(this.selectors.orderSummary.lineItemTitle);
                const priceEl = item.locator(this.selectors.orderSummary.lineItemPrice);
                
                if (await titleEl.isVisible() && await priceEl.isVisible()) {
                    const name = await titleEl.textContent();
                    const price = await priceEl.textContent();
                    
                    summary.items.push({
                        name: name?.trim() || '',
                        price: price?.trim() || ''
                    });
                }
            }
            
            // Fallback if no items found using direct line item approach
            if (summary.items.length === 0) {
                // Try alternative approach to find product items
                const titles = this.page.locator(this.selectors.orderSummary.lineItemTitle);
                const prices = this.page.locator(this.selectors.orderSummary.lineItemPrice);
                
                const titlesCount = await titles.count();
                const pricesCount = await prices.count();
                
                const count = Math.min(titlesCount, pricesCount);
                
                for (let i = 0; i < count; i++) {
                    const name = await titles.nth(i).textContent();
                    const price = await prices.nth(i).textContent();
                    
                    summary.items.push({
                        name: name?.trim() || '',
                        price: price?.trim() || ''
                    });
                }
            }
            
            // Get the summary values
            const subtotalEl = this.page.locator(this.selectors.orderSummary.subtotal);
            const shippingEl = this.page.locator(this.selectors.orderSummary.shippingCost);
            const totalEl = this.page.locator(this.selectors.orderSummary.total);
            
            if (await subtotalEl.isVisible()) {
                summary.subtotal = await subtotalEl.textContent() || '';
            }
            
            if (await shippingEl.isVisible()) {
                summary.shipping = await shippingEl.textContent() || '';
            }
            
            if (await totalEl.isVisible()) {
                summary.total = await totalEl.textContent() || '';
            }
            
            console.log('Order summary:', summary);
            return summary;
        } catch (error) {
            console.error(`Error getting order summary: ${error.message}`);
            // Return the partial summary rather than failing
            return summary;
        }
    }

    /**
     * Select a shipping method
     * @param {string} [methodValue] - The value of the shipping method to select (if not provided, selects the first available)
     */
    async selectShippingMethod(methodValue) {
        console.log('Selecting shipping method');
        
        try {
            // Wait for shipping methods to be visible
            await this.page.waitForSelector(this.selectors.shipping.methodContainer, {
                state: 'visible',
                timeout: 10000
            });
            
            // Get all available methods
            const methods = this.page.locator(this.selectors.shipping.methods);
            const count = await methods.count();
            
            console.log(`Found ${count} shipping methods`);
            
            if (count === 0) {
                console.log('No shipping methods found, continuing without selection');
                return;
            }
            
            // If no specific method provided, select the first one
            if (!methodValue) {
                const firstMethod = methods.first();
                methodValue = await firstMethod.getAttribute('value');
                console.log(`No method specified, using first available: ${methodValue}`);
            }
            
            // Find the label for the shipping method
            const methodLabel = this.page.locator(this.selectors.shipping.methodLabel(methodValue));
            
            if (await methodLabel.isVisible()) {
                await methodLabel.click();
                console.log(`Selected shipping method: ${methodValue}`);
            } else {
                // If the specific method is not found, select the first one
                await methods.first().click();
                console.log(`Specified method ${methodValue} not found, selected first available`);
            }
            
            // Wait for any updates after selection
            await this.page.waitForTimeout(1000);
        } catch (error) {
            console.error(`Error selecting shipping method: ${error.message}`);
            // Continue without failing the test
            console.log('Continuing without shipping method selection');
        }
    }

    /**
     * Continue to the payment section
     */
    async continueToPayment() {
        console.log('Continuing to payment section');
        
        try {
            // Take screenshot before continuing
            await this.page.screenshot({ path: `before-continue-to-payment-${Date.now()}.png` });
            
            const continueButton = this.page.locator(this.selectors.shipping.continueButton);
            
            if (await continueButton.isVisible()) {
                await continueButton.click();
                console.log('Clicked continue to payment button');
                
                // Wait for payment section to be visible
                await this.page.waitForSelector(this.selectors.payment.section, {
                    state: 'visible',
                    timeout: 10000
                });
            } else {
                console.log('Continue to payment button not found, assuming already on payment section');
            }
            
            // Take screenshot after continuing
            await this.page.screenshot({ path: `after-continue-to-payment-${Date.now()}.png` });
        } catch (error) {
            console.error(`Error continuing to payment: ${error.message}`);
            // Try to proceed anyway
            console.log('Attempting to continue despite error');
        }
    }

    /**
     * Select a payment method
     * @param {string} method - The payment method to select ('creditCard', 'paypal', or 'klarna')
     */
    async selectPaymentMethod(method) {
        console.log(`Selecting payment method: ${method}`);
        
        try {
            let selector;
            
            switch (method.toLowerCase()) {
                case 'credit_card':
                case 'creditcard':
                case 'card':
                    selector = this.selectors.payment.methods.creditCardOption;
                    break;
                    
                case 'paypal':
                    selector = this.selectors.payment.methods.paypalOption;
                    break;
                    
                case 'klarna':
                    selector = this.selectors.payment.methods.klarnaOption;
                    break;
                    
                default:
                    console.warn(`Unknown payment method: ${method}, defaulting to credit card`);
                    selector = this.selectors.payment.methods.creditCardOption;
            }
            
            // Check if the payment method is visible
            const methodElement = this.page.locator(selector);
            
            if (await methodElement.isVisible()) {
                await methodElement.click();
                console.log(`Selected payment method: ${method}`);
                
                // Wait for payment method specific fields to appear
                await this.page.waitForTimeout(1000);
            } else {
                console.warn(`Payment method ${method} not found, using default`);
            }
        } catch (error) {
            console.error(`Error selecting payment method: ${error.message}`);
            throw new Error(`Failed to select payment method: ${error.message}`);
        }
    }

    /**
     * Fill the credit card details
     * @param {Object} cardDetails - The credit card details
     * @param {string} cardDetails.number - Card number
     * @param {string} cardDetails.expiry - Expiry date (MM/YY)
     * @param {string} cardDetails.cvc - CVC code
     */
    async fillCreditCardInfo(cardDetails) {
        console.log('Filling credit card information');
        
        try {
            // Fill card number iframe
            const cardNumberFrame = this.page.frameLocator(this.selectors.payment.creditCard.numberFrame);
            await cardNumberFrame.locator(this.selectors.payment.creditCard.numberInput).fill(cardDetails.number);
            
            // Fill expiry date iframe
            const cardExpiryFrame = this.page.frameLocator(this.selectors.payment.creditCard.expiryFrame);
            await cardExpiryFrame.locator(this.selectors.payment.creditCard.expiryInput).fill(cardDetails.expiry);
            
            // Fill CVC iframe
            const cardCvcFrame = this.page.frameLocator(this.selectors.payment.creditCard.cvcFrame);
            await cardCvcFrame.locator(this.selectors.payment.creditCard.cvcInput).fill(cardDetails.cvc);
            
            console.log('Credit card details filled successfully');
        } catch (error) {
            console.error(`Error filling credit card details: ${error.message}`);
            throw new Error(`Failed to fill credit card details: ${error.message}`);
        }
    }

    /**
     * Complete the order
     * @returns {Promise<void>}
     */
    async completeOrder() {
        console.log('Completing order');
        
        try {
            // Take screenshot before completing order
            await this.page.screenshot({ path: `before-complete-order-${Date.now()}.png` });
            
            // Click the complete order button
            const completeButton = this.page.locator(this.selectors.payment.completeOrderButton);
            await completeButton.click();
            
            // Wait for navigation to either upsell or confirmation page
            console.log('Waiting for navigation after completing order');
            
            await Promise.race([
              //  this.page.waitForURL('**/upsell/**', { timeout: 60000 }),
               // this.page.waitForURL('**/confirmation/**', { timeout: 60000 }),
                this.page.waitForURL('**/thank-you', { timeout: 60000 })
            ]);
            
            // Take screenshot after completing order
            await this.page.screenshot({ path: `after-complete-order-${Date.now()}.png` });
            
            console.log(`Order completed, navigated to: ${this.page.url()}`);
        } catch (error) {
            console.error(`Error completing order: ${error.message}`);
            throw new Error(`Failed to complete order: ${error.message}`);
        }
    }
}

module.exports = { SalesFunnelPaymentPage }; 