# Troubleshooting Guide

## Overview

This guide provides solutions for common issues encountered when using the Playwright-BrowserStack multi-brand e-commerce testing framework. Issues are organized by category with step-by-step resolution instructions.

## TestDataManager Issues

### Issue: TestDataManager Initialization Failed

**Symptoms:**
- Error: "Test data not initialized. Call initialize() first."
- Error: "Product with key X not found in test data"
- Error: "Test data file not found"

**Solutions:**

1. **Verify YAML file structure:**
```bash
# Check if brand data files exist
ls -la tests/data/brands/aeons/
ls -la tests/data/brands/dss/
ls -la tests/data/brands/ypn/

# Validate YAML syntax
node -e "
const yaml = require('js-yaml');
const fs = require('fs');
try {
  const content = fs.readFileSync('tests/data/brands/aeons/test_data.yml', 'utf8');
  yaml.load(content);
  console.log('✅ YAML syntax is valid');
} catch (error) {
  console.log('❌ YAML syntax error:', error.message);
}
"
```

2. **Check working directory:**
```bash
# Ensure you're in the project root
pwd
ls -la | grep -E "(tests|package.json)"

# If in tests directory, go back to root
cd ..
```

3. **Verify brand and environment:**
```bash
# Check available brands
ls tests/data/brands/

# Test initialization manually
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'aeons', 'stage');
console.log('✅ Initialization successful');
console.log('Available products:', Object.keys(manager.productsData || {}));
"
```

### Issue: Product Not Found

**Symptoms:**
- Error: "Product with key X not found in test data"

**Solutions:**

1. **Check available products:**
```bash
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'aeons', 'stage');
console.log('Available products:');
console.log(Object.keys(manager.productsData || {}));
"
```

2. **Use correct product key format:**
```javascript
// Try different key variations
const productKeys = [
  'sunrise_spark',      // snake_case
  'sunrise-spark',      // kebab-case
  'aeons_sunrise_spark', // with brand prefix
  'aeons-sunrise-spark'  // with brand prefix kebab-case
];
```

3. **Check product slug vs key:**
```bash
# View product structure
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'aeons', 'stage');
console.log('Product structure:');
console.log(JSON.stringify(manager.productsData, null, 2));
"
```

## BrowserStack Issues

### Issue: BrowserStack Authentication Failed

**Symptoms:**
- Error: "Authentication failed"
- Error: "Invalid username or access key"
- Tests hang during BrowserStack connection

**Solutions:**

1. **Verify credentials:**
```bash
# Check environment variables
echo "Username: $BROWSERSTACK_USERNAME"
echo "Access Key: ${BROWSERSTACK_ACCESS_KEY:0:8}..." # Show first 8 chars only

# Test API connection
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  https://api.browserstack.com/automate/plan.json
```

2. **Check account status:**
```bash
# Check account plan and limits
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  https://api.browserstack.com/automate/plan.json | jq '.'
```

3. **Verify SDK configuration:**
```bash
# Check browserstack.yml exists
ls -la browserstack.yml

# Validate browserstack.yml syntax
node -e "
const yaml = require('yaml');
const fs = require('fs');
try {
  const content = fs.readFileSync('browserstack.yml', 'utf8');
  const config = yaml.parse(content);
  console.log('✅ BrowserStack config valid');
  console.log('Configured platforms:', Object.keys(config.platforms || {}));
} catch (error) {
  console.log('❌ BrowserStack config error:', error.message);
}
"
```

### Issue: BrowserStack Session Management

**Symptoms:**
- Multiple sessions running simultaneously
- Session not found errors
- Tests failing due to session conflicts

**Solutions:**

1. **Check concurrent sessions:**
```bash
# Check running sessions
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  https://api.browserstack.com/automate/sessions.json | jq '.[] | select(.status == "running")'
```

2. **Ensure single worker configuration:**
```bash
# Verify worker count in commands
grep -r "workers" package.json
grep -r "WORKERS" .env

# Force single worker
node run-test.js tests/regression/main-purchase.spec.js --workers=1 --platform=windows-chrome --brand=aeons --env=stage
```

3. **Clean up sessions:**
```bash
# Stop all running sessions (use with caution)
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  -X PUT https://api.browserstack.com/automate/sessions/<session_id>.json \
  -d '{"status":"completed"}'
```

### Issue: Mobile Device Testing Problems

**Symptoms:**
- Tests running on emulators instead of real devices
- Mobile-specific functionality not working
- Touch interactions failing

**Solutions:**

1. **Verify real device configuration:**
```bash
# Check platform configuration
node -e "
const config = require('./playwright.config.js');
console.log('Mobile projects:');
config.projects.forEach(project => {
  if (project.name.includes('galaxy') || project.name.includes('iphone')) {
    console.log(project.name, project.use);
  }
});
"
```

2. **Force BrowserStack for mobile:**
```bash
# Always use BrowserStack for mobile platforms
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

3. **Check device availability:**
```bash
# Check available devices
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  https://api.browserstack.com/automate/browsers.json | jq '.[] | select(.device != null)'
```

## Email Testing Issues

### Issue: Mailtrap Connection Failed

**Symptoms:**
- Error: "Mailtrap API Token not found"
- Error: "Failed to retrieve emails"
- Email verification tests timing out

**Solutions:**

1. **Verify Mailtrap credentials:**
```bash
# Check environment variables
echo "API Token: ${MAILTRAP_API_TOKEN:0:8}..."
echo "Inbox ID: $MAILTRAP_INBOX_ID"

# Test API connection
curl -H "Authorization: Bearer $MAILTRAP_API_TOKEN" \
  https://mailtrap.io/api/v1/inboxes
```

2. **Check inbox configuration:**
```bash
# Verify inbox exists and is accessible
curl -H "Authorization: Bearer $MAILTRAP_API_TOKEN" \
  https://mailtrap.io/api/v1/inboxes/$MAILTRAP_INBOX_ID
```

3. **Test email retrieval:**
```bash
# Check recent emails
curl -H "Authorization: Bearer $MAILTRAP_API_TOKEN" \
  "https://mailtrap.io/api/v1/inboxes/$MAILTRAP_INBOX_ID/messages?limit=5"
```

### Issue: Email Verification Timeout

**Symptoms:**
- Tests timeout waiting for emails
- Emails not arriving in Mailtrap
- Email content verification failing

**Solutions:**

1. **Increase timeout for email tests:**
```javascript
// In test files, increase timeout
test.setTimeout(300000); // 5 minutes for email tests
```

2. **Check email sending configuration:**
```bash
# Verify test email address format
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
const manager = new TestDataManager();
manager.initialize('default', 'aeons', 'stage');
const user = manager.getUser('default');
console.log('Test email:', user.email);
"
```

3. **Debug email helper:**
```bash
# Test email helper directly
node -e "
const { MailtrapHelper } = require('./src/utils/email/mailtrap-helper');
const helper = new MailtrapHelper({
  apiToken: process.env.MAILTRAP_API_TOKEN,
  inboxId: process.env.MAILTRAP_INBOX_ID
});
helper.getEmails().then(emails => {
  console.log('Recent emails:', emails.length);
}).catch(error => {
  console.log('Error:', error.message);
});
"
```

## Database Connection Issues

### Issue: SSH Tunnel Connection Failed

**Symptoms:**
- Error: "SSH connection failed"
- Error: "Database connection timeout"
- Database tests failing with connection errors

**Solutions:**

1. **Verify SSH configuration:**
```bash
# Check SSH environment variables
echo "SSH Host: $SSH_HOST"
echo "SSH User: $SSH_USER"
echo "SSH Port: $SSH_PORT"

# Test SSH connection manually
ssh -i ~/.ssh/id_rsa $SSH_USER@$SSH_HOST -p $SSH_PORT
```

2. **Check SSH key permissions:**
```bash
# Verify key file permissions
ls -la ~/.ssh/id_rsa
chmod 600 ~/.ssh/id_rsa

# Test key authentication
ssh-add ~/.ssh/id_rsa
ssh-add -l
```

3. **Verify database configuration:**
```bash
# Check database environment variables
echo "DB Host: $DB_HOST"
echo "DB Port: $DB_PORT"
echo "DB User: $DB_USER"
echo "Local Port: $LOCAL_DB_PORT"
```

4. **Test tunnel manually:**
```bash
# Create manual SSH tunnel
ssh -L $LOCAL_DB_PORT:$DB_HOST:$DB_PORT $SSH_USER@$SSH_HOST -N &

# Test database connection through tunnel
mysql -h 127.0.0.1 -P $LOCAL_DB_PORT -u $DB_USER -p $DB_NAME

# Kill tunnel
pkill -f "ssh -L $LOCAL_DB_PORT"
```

## Visual Testing Issues

### Issue: Screenshot Upload Failed

**Symptoms:**
- Error: "Failed to upload screenshot to Cloudinary"
- Visual tests not generating screenshots
- Gemini AI analysis failing

**Solutions:**

1. **Verify Cloudinary configuration:**
```bash
# Check Cloudinary credentials
echo "Cloud Name: $CLOUDINARY_CLOUD_NAME"
echo "API Key: ${CLOUDINARY_API_KEY:0:8}..."

# Test Cloudinary connection
curl -X GET \
  "https://api.cloudinary.com/v1_1/$CLOUDINARY_CLOUD_NAME/resources/image" \
  -u "$CLOUDINARY_API_KEY:$CLOUDINARY_API_SECRET"
```

2. **Check Gemini AI configuration:**
```bash
# Verify Gemini API key
echo "Gemini API Key: ${GOOGLE_AI_API_KEY:0:8}..."

# Test Gemini API
curl -X POST \
  "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=$GOOGLE_AI_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"contents":[{"parts":[{"text":"Hello"}]}]}'
```

3. **Debug visual analysis helper:**
```bash
# Test visual analysis components
node -e "
const { VisualAnalysisHelper } = require('./src/utils/visual-analisys-helper');
console.log('Visual analysis helper loaded successfully');
"
```

## Platform-Specific Issues

### Issue: Windows Chrome Local Testing

**Symptoms:**
- Chrome not launching locally
- Tests failing on Windows Chrome
- Browser crashes or hangs

**Solutions:**

1. **Update Chrome and Playwright:**
```bash
# Update Playwright browsers
npx playwright install chromium
npx playwright install-deps

# Check Chrome version
google-chrome --version  # Linux
"C:\Program Files\Google\Chrome\Application\chrome.exe" --version  # Windows
```

2. **Check system resources:**
```bash
# Monitor system resources during test execution
top  # Linux/Mac
taskmgr  # Windows
```

### Issue: Safari Testing on Mac

**Symptoms:**
- Safari tests failing locally
- Permission issues with Safari
- Safari not launching

**Solutions:**

1. **Enable Safari automation:**
```bash
# Enable Safari Developer menu
defaults write com.apple.Safari IncludeInternalDebugMenu 1

# Enable automation
sudo safaridriver --enable
```

2. **Use BrowserStack for Safari:**
```bash
# Prefer BrowserStack for Safari testing
node run-test.js tests/regression/main-purchase.spec.js --platform=mac-safari --brand=aeons --env=stage --browserstack=true
```

## Performance Issues

### Issue: Tests Running Slowly

**Symptoms:**
- Tests taking longer than expected
- Timeouts occurring frequently
- Poor performance on mobile devices

**Solutions:**

1. **Optimize test timeouts:**
```bash
# Increase timeouts for slow environments
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --timeout=300000
```

2. **Use appropriate platforms:**
```bash
# Use desktop for complex tests
node run-test.js tests/regression/sales-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Use mobile only for mobile-specific tests
node run-test.js tests/regression/responsive.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

3. **Monitor BrowserStack queue:**
```bash
# Check BrowserStack queue status
curl -u "$BROWSERSTACK_USERNAME:$BROWSERSTACK_ACCESS_KEY" \
  https://api.browserstack.com/automate/plan.json | jq '.queued_sessions'
```

## CI/CD Issues

### Issue: CI Tests Failing

**Symptoms:**
- Tests pass locally but fail in CI
- Environment variable issues in CI
- Artifact download failures

**Solutions:**

1. **Verify CI environment variables:**
```bash
# In CI environment, check variables are set
env | grep -E "(BROWSERSTACK|MAILTRAP|SSH|DB)_"
```

2. **Use CI-specific commands:**
```bash
# Use CI runner instead of direct commands
npm run ci:test
npm run ci:test:bs
```

3. **Check CI logs:**
```bash
# Enable debug logging in CI
DEBUG=* npm run ci:test
```

## Getting Additional Help

### Debug Information Collection

When reporting issues, collect this information:

```bash
# System information
node --version
npm --version
npx playwright --version

# Environment check
env | grep -E "(BRAND|TEST_ENV|PLATFORM|BROWSERSTACK|MAILTRAP)" | sort

# Test framework status
node -e "
const TestDataManager = require('./tests/data/test-data-manager');
console.log('TestDataManager available:', !!TestDataManager);
"

# Recent test results
ls -la test-results/ | head -10
```

### Log Analysis

```bash
# Check recent logs
tail -f log/usage.log
tail -f log/events.json

# Search for specific errors
grep -r "Error" test-results/
grep -r "Failed" test-results/
```

### Contact Information

- **Framework Issues**: Check GitHub issues or internal documentation
- **BrowserStack Issues**: BrowserStack support portal
- **Mailtrap Issues**: Mailtrap support documentation
- **Infrastructure Issues**: Contact system administrator

## Prevention Tips

1. **Regular Maintenance:**
   - Update dependencies monthly
   - Rotate credentials quarterly
   - Clean up old test results weekly

2. **Monitoring:**
   - Monitor BrowserStack usage and limits
   - Check email quota in Mailtrap
   - Monitor database connection health

3. **Best Practices:**
   - Always use single worker for BrowserStack
   - Prefer real devices for mobile testing
   - Use appropriate timeouts for different test types
   - Keep environment variables secure and up-to-date
