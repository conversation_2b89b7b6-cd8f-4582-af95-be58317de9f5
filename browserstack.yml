# BrowserStack Base Configuration
# This file contains the base settings used by the CI/CD script for dynamic config generation

# Authentication (will be overridden by environment variables if present)
userName: ${BROWSERSTACK_USERNAME}
accessKey: ${BROWSERSTACK_ACCESS_KEY}

# Common settings
framework: playwright
debug: true
networkLogs: true
consoleLogs: debug
local: false
accessibility: false

# Default limits
parallelsPerPlatform: 1
maxParallelSessions: 2

# Note: platforms, buildName, and projectName will be generated dynamically by ci/run-env-tests.js 