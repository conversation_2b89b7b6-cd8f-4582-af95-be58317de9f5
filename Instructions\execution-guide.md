# Test Execution Guide

## Overview

This guide provides comprehensive instructions for executing tests in the Playwright-BrowserStack multi-brand e-commerce testing framework. All examples use the project's custom `run-test.js` runner which provides enhanced functionality over standard Playwright commands.

## Basic Execution Patterns

### Single Test Execution

```bash
# Run a specific test file locally
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Run a specific test on BrowserStack
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true

# Run with specific test data set
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --dataset=staging
```

### Directory-Based Execution

```bash
# Run all regression tests
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage

# Run all validation tests
node run-test.js tests/validation/ --platform=windows-chrome --brand=aeons --env=stage

# Run all tests in a directory on BrowserStack
node run-test.js tests/regression/ --platform=iphone-14 --brand=aeons --env=stage --browserstack=true
```

### Tag-Based Execution

```bash
# Run smoke tests
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@smoke

# Run regression tests
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@regression

# Run visual tests
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@visual

# Run specific test category
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@purchase
```

## Platform-Specific Execution

### Desktop Platforms

#### Windows Chrome
```bash
# Local execution
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# BrowserStack execution
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --browserstack=true

# With debug mode
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --debug=true
```

#### Mac Safari
```bash
# BrowserStack execution (recommended for Safari)
node run-test.js tests/regression/main-purchase.spec.js --platform=mac-safari --brand=aeons --env=stage --browserstack=true

# Local execution (if running on Mac)
node run-test.js tests/regression/main-purchase.spec.js --platform=mac-safari --brand=aeons --env=stage
```

#### Firefox
```bash
# Local execution
node run-test.js tests/regression/main-purchase.spec.js --platform=firefox --brand=aeons --env=stage

# BrowserStack execution
node run-test.js tests/regression/main-purchase.spec.js --platform=firefox --brand=aeons --env=stage --browserstack=true
```

### Mobile Platforms

#### Samsung Galaxy S23 (Android)
```bash
# BrowserStack execution (real device)
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true

# Local emulation (limited functionality)
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage

# Mobile-specific responsive tests
node run-test.js tests/regression/responsive.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

#### iPhone 14 (iOS)
```bash
# BrowserStack execution (real device)
node run-test.js tests/regression/main-purchase.spec.js --platform=iphone-14 --brand=aeons --env=stage --browserstack=true

# Mobile-optimized tests
node run-test.js tests/regression/responsive.spec.js --platform=iphone-14 --brand=aeons --env=stage --browserstack=true
```

## Brand-Specific Execution

### AEONS Brand Testing

```bash
# Complete AEONS regression suite
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage

# AEONS sales funnel tests
node run-test.js tests/regression/sales-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage

# AEONS subscription tests
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=aeons --env=stage

# AEONS mobile testing
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

### DSS Brand Testing

```bash
# DSS main purchase flow
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=dss --env=stage

# DSS Shopify migration tests
node run-test.js tests/shopify/dss/ --platform=windows-chrome --brand=dss --env=stage

# DSS abandoned cart testing
node run-test.js tests/regression/abandoned-cart-email.spec.js --platform=windows-chrome --brand=dss --env=stage

# DSS content verification
node run-test.js tests/shopify/dss/content.spec.js --platform=windows-chrome --brand=dss --env=stage
```

### YPN Brand Testing

```bash
# YPN main purchase flow
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=ypn --env=stage

# YPN subscription tests
node run-test.js tests/regression/subscription-purchase-creditcard.spec.js --platform=windows-chrome --brand=ypn --env=stage

# YPN Shopify tests
node run-test.js tests/shopify/ypn/ --platform=windows-chrome --brand=ypn --env=stage
```

## Environment-Specific Execution

### Development Environment

```bash
# Dev environment testing
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=dev

# Dev smoke tests
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=dev --tags=@smoke

# Dev with BrowserStack
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=dev --browserstack=true
```

### Staging Environment (Default)

```bash
# Staging environment (default)
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage

# Staging regression suite
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage

# Staging with specific data set
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --dataset=staging
```

### Production Environment

```bash
# Production smoke tests only
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=prod --tags=@smoke --dataset=production

# Production content verification
node run-test.js tests/regression/content.spec.js --platform=windows-chrome --brand=aeons --env=prod --dataset=production

# Production visual tests
node run-test.js tests/visual/ --platform=windows-chrome --brand=aeons --env=prod --dataset=production
```

## Advanced Execution Scenarios

### Multi-Platform Sequential Testing

```bash
# Use the CI sequential runner for multiple platforms
node ci/run-sequential-tests.js stage tests/regression/main-purchase.spec.js @stage_one_time_smoke windows-chrome

# Custom sequential execution
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage && \
node run-test.js tests/regression/main-purchase.spec.js --platform=samsung-galaxy-s23 --brand=aeons --env=stage --browserstack=true
```

### Cross-Brand Testing

```bash
# Test same functionality across all brands
for brand in aeons dss ypn; do
  node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=$brand --env=stage
done

# Cross-brand content verification
for brand in aeons dss ypn; do
  node run-test.js tests/regression/content.spec.js --platform=windows-chrome --brand=$brand --env=stage
done
```

### Performance and Load Testing

```bash
# Single worker (BrowserStack limitation)
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage --workers=1

# Extended timeout for slow tests
node run-test.js tests/regression/sales-funnel-backend-complete.spec.js --platform=windows-chrome --brand=aeons --env=stage --timeout=300000
```

### Debug and Development

```bash
# Debug mode with verbose logging
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --debug=true

# Run with specific test data for debugging
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --dataset=debug

# Visual test with screenshot analysis
node run-test.js tests/visual/visual.spec.js --platform=windows-chrome --brand=aeons --env=stage --debug=true
```

## NPM Script Shortcuts

### Basic Commands

```bash
# Smoke tests
npm run test:smoke

# Regression tests
npm run test:regression

# Visual tests
npm run test:visual

# BrowserStack smoke tests
npm run test:smoke:bs
```

### Platform-Specific NPM Commands

```bash
# Desktop browsers
npm run test:chrome
npm run test:safari
npm run test:firefox

# Mobile devices
npm run test:android
npm run test:ios

# BrowserStack variants
npm run test:chrome:bs
npm run test:android:bs
npm run test:ios:bs
```

### Environment-Specific NPM Commands

```bash
# Development environment
npm run test:dev
npm run test:dev:bs

# Staging environment
npm run test:stage
npm run test:stage:bs

# Production environment
npm run test:prod
npm run test:prod:bs
```

### CI/CD Commands

```bash
# CI test execution
npm run ci:test

# CI with BrowserStack
npm run ci:test:bs

# Sequential testing
npm run ci:test:sequential

# Environment-specific CI
npm run ci:test:stage:chrome
npm run ci:test:dev:android:bs
```

## Test Output and Reporting

### Local Test Reports

```bash
# Generate and view HTML report
npm run report

# BrowserStack report
npm run report:bs
```

### Test Results Location

- **Local Results**: `test-results/`
- **HTML Reports**: `playwright-report/`
- **Screenshots**: `screenshots/`
- **Test Archives**: `test-archives/`

### BrowserStack Integration

```bash
# View BrowserStack session details
# Session ID will be logged during test execution
# Access BrowserStack dashboard for detailed results
```

## Common Execution Patterns

### Quick Validation

```bash
# Quick smoke test across platforms
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@smoke
node run-test.js tests/ --platform=samsung-galaxy-s23 --brand=aeons --env=stage --tags=@smoke --browserstack=true
```

### Comprehensive Testing

```bash
# Full regression suite
node run-test.js tests/regression/ --platform=windows-chrome --brand=aeons --env=stage
node run-test.js tests/validation/ --platform=windows-chrome --brand=aeons --env=stage
node run-test.js tests/visual/ --platform=windows-chrome --brand=aeons --env=stage
```

### Pre-Deployment Validation

```bash
# Pre-deployment smoke tests
node run-test.js tests/ --platform=windows-chrome --brand=aeons --env=stage --tags=@smoke
node run-test.js tests/ --platform=iphone-14 --brand=aeons --env=stage --tags=@smoke --browserstack=true

# Critical flow validation
node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage
node run-test.js tests/regression/sales-funnel-upsell.spec.js --platform=windows-chrome --brand=aeons --env=stage
```

## Troubleshooting Execution Issues

### Common Command Issues

```bash
# If test path not found
ls -la tests/regression/  # Verify file exists

# If platform not supported
node run-test.js --help  # See available platforms

# If BrowserStack connection fails
echo $BROWSERSTACK_USERNAME  # Verify credentials
echo $BROWSERSTACK_ACCESS_KEY
```

### Debug Execution

```bash
# Enable debug logging
DEBUG=* node run-test.js tests/regression/main-purchase.spec.js --platform=windows-chrome --brand=aeons --env=stage --debug=true

# Check environment variables
env | grep -E "(BRAND|TEST_ENV|PLATFORM|BROWSERSTACK)"
```

## Next Steps

- Review [Environment Setup](./environment-setup.md) for configuration requirements
- Check [Troubleshooting](./troubleshooting.md) for common issues
- Explore [Brand-Specific Guides](./brand-specific-guides/) for specialized instructions
