/**
 * Page object for the initial sales funnel checkout page
 */
class SalesFunnelInitialCheckoutPage {
    /**
     * @param {import('@playwright/test').Page} page
     */
    constructor(page) {
        this.page = page;
        this.selectors = {
            // Product details
            productDetails: {
                container: '.product-presentation, .product-details',
                title: '.product-title, h1, .product-name',
                price: '.price, .product-price',
                description: '.product-description'
            },
            
            // Form elements
            form: {
                container: '#app_one_page_checkout',
                email: '#app_one_page_checkout_customer_email',
                
                // Billing address
                billingFirstName: '#app_one_page_checkout_billingAddress_firstName',
                billingLastName: '#app_one_page_checkout_billingAddress_lastName',
                billingPhone: '#app_one_page_checkout_billingAddress_phoneNumber',
                billingAddress1: '#app_one_page_checkout_billingAddress_street',
                billingAddress2: '#app_one_page_checkout_billingAddress_apt',
                billingCity: '#app_one_page_checkout_billingAddress_city',
                billingPostcode: '#app_one_page_checkout_billingAddress_postcode',
                billingCountry: '#app_one_page_checkout_billingAddress_countryCode',
                
                // Shipping options
                sameAsBilling: '#app_one_page_checkout_differentShippingAddress_0',
                differentShipping: '#app_one_page_checkout_differentShippingAddress_1',
                
                // Shipping address (only visible when different shipping is selected)
                shippingFirstName: '#app_one_page_checkout_shippingAddress_firstName',
                shippingLastName: '#app_one_page_checkout_shippingAddress_lastName',
                shippingPhone: '#app_one_page_checkout_shippingAddress_phoneNumber',
                shippingAddress1: '#app_one_page_checkout_shippingAddress_street',
                shippingAddress2: '#app_one_page_checkout_shippingAddress_apt',
                shippingCity: '#app_one_page_checkout_shippingAddress_city',
                shippingPostcode: '#app_one_page_checkout_shippingAddress_postcode',
                shippingCountry: '#app_one_page_checkout_shippingAddress_countryCode',
                
                // Continue buttons
                continueButton: '.checkout-v3-continue-to-shipping'
            }
        };
    }

    /**
     * Navigate to a specific sales funnel URL
     * @param {string} funnelCode - The funnel code to navigate to
     */
    async navigate(funnelCode) {
        await this.page.goto(`/specials/start/${funnelCode}`);
        await this.waitForPageLoad();
    }

    /**
     * Wait for the page to load
     */
    async waitForPageLoad() {
        console.log('Inside waitForPageLoad method.');
        console.log(`Waiting for selector: ${this.selectors.form.email}`);
        await this.page.waitForSelector(this.selectors.form.email, { state: 'visible' });
        console.log('Selector found. Taking screenshot.');
        
        // Take a screenshot for debugging
        await this.page.screenshot({ path: `sales-funnel-initial-${Date.now()}.png` });
        console.log('Screenshot taken. Exiting waitForPageLoad.');
    }

    /**
     * Get the product details from the page
     * @returns {Promise<{name: string, price: string}>}
     */
    async getProductDetails() {
        console.log('Getting product details from sales funnel initial page');

        try {
            const container = this.page.locator(this.selectors.productDetails.container);
            await container.waitFor({ state: 'visible', timeout: 5000 });

            const name = await this.page.locator(this.selectors.productDetails.title).textContent();
            const price = await this.page.locator(this.selectors.productDetails.price).textContent();

            console.log(`Found product: ${name} with price: ${price}`);
            return {
                name: name?.trim() || '',
                price: price?.trim() || ''
            };
        } catch (error) {
            console.error(`Error getting product details: ${error.message}`);
            // Return a fallback to avoid test failures
            return {
                name: 'Product Name Unavailable',
                price: 'Price Unavailable'
            };
        }
    }

    /**
     * Fill the customer email
     * @param {string} email - Customer email address
     */
    async fillEmail(email) {
        console.log(`Attempting to fill email with: ${email}`);
        await this.page.fill(this.selectors.form.email, email);
        console.log('Email field filled.');
    }

    /**
     * Fill the billing address fields
     * @param {Object} address - The billing address details
     * @param {string} address.firstName - First name
     * @param {string} address.lastName - Last name
     * @param {string} address.phone - Phone number
     * @param {string} address.address1 - Address line 1
     * @param {string} [address.address2] - Address line 2 (optional)
     * @param {string} address.city - City
     * @param {string} address.postcode - Postal code
     * @param {string} address.country - Country code (e.g., 'GB')
     */
    async fillBillingAddress(address) {
        console.log('Filling billing address');
        
        try {
            await this.page.fill(this.selectors.form.billingFirstName, address.firstName);
            await this.page.fill(this.selectors.form.billingLastName, address.lastName);
            await this.page.fill(this.selectors.form.billingPhone, address.phone);
            await this.page.fill(this.selectors.form.billingAddress1, address.address1);
            
            if (address.address2) {
                await this.page.fill(this.selectors.form.billingAddress2, address.address2);
            }
            
            await this.page.fill(this.selectors.form.billingCity, address.city);
            await this.page.fill(this.selectors.form.billingPostcode, address.postcode);
            
            // Handle country selection - check if the option exists first
            const countrySelector = this.selectors.form.billingCountry;
            await this.page.waitForSelector(countrySelector, { state: 'visible' });
            
            // Get available options
            const options = await this.page.$$eval(`${countrySelector} option`, options => 
                options.map(option => ({ value: option.value, text: option.textContent }))
            );
            
          //  console.log('Available country options:', options);
            
            // Try to select by value first
            try {
                await this.page.selectOption(countrySelector, { value: address.country });
            } catch (error) {
                console.warn(`Could not select country by value: ${error.message}`);
                
                // Try to select by visible text (case insensitive)
                try {
                    const matchingOption = options.find(opt => 
                        opt.text.toLowerCase().includes(address.country.toLowerCase()) ||
                        address.country.toLowerCase().includes(opt.text.toLowerCase())
                    );
                    
                    if (matchingOption) {
                        console.log(`Selecting country by text match: ${matchingOption.text}`);
                        await this.page.selectOption(countrySelector, { value: matchingOption.value });
                    } else {
                        // Just select the first option as fallback
                        console.log('No matching country found, selecting first option');
                        await this.page.selectOption(countrySelector, { index: 1 });
                    }
                } catch (fallbackError) {
                    console.error(`Fallback country selection failed: ${fallbackError.message}`);
                }
            }
        } catch (error) {
            console.error(`Error filling billing address: ${error.message}`);
            throw new Error(`Failed to fill billing address: ${error.message}`);
        }
    }

    /**
     * Set whether to use the same address for shipping
     * @param {boolean} useSameAddress - Whether to use the same address for shipping
     */
    async useSameAddressForShipping(useSameAddress = true) {
        const selector = useSameAddress 
            ? this.selectors.form.sameAsBilling 
            : this.selectors.form.differentShipping;
        
        const isChecked = await this.page.isChecked(selector);
        if (!isChecked) {
            await this.page.check(selector);
        }
    }

    /**
     * Fill the shipping address (only if different shipping address is selected)
     * @param {Object} address - The shipping address details
     * @param {string} address.firstName - First name
     * @param {string} address.lastName - Last name
     * @param {string} address.phone - Phone number
     * @param {string} address.address1 - Address line 1
     * @param {string} [address.address2] - Address line 2 (optional)
     * @param {string} address.city - City
     * @param {string} address.postcode - Postal code
     * @param {string} address.country - Country code (e.g., 'GB')
     */
    async fillShippingAddress(address) {
        console.log('Filling shipping address');
        
        try {
            // First select different shipping address option
            await this.useSameAddressForShipping(false);
            
            // Wait for shipping fields to be visible
            await this.page.waitForSelector(this.selectors.form.shippingFirstName, { state: 'visible' });
            
            // Fill shipping address fields
            await this.page.fill(this.selectors.form.shippingFirstName, address.firstName);
            await this.page.fill(this.selectors.form.shippingLastName, address.lastName);
            await this.page.fill(this.selectors.form.shippingPhone, address.phone);
            await this.page.fill(this.selectors.form.shippingAddress1, address.address1);
            
            if (address.address2) {
                await this.page.fill(this.selectors.form.shippingAddress2, address.address2);
            }
            
            await this.page.fill(this.selectors.form.shippingCity, address.city);
            await this.page.fill(this.selectors.form.shippingPostcode, address.postcode);
            
            // Handle country selection
            await this.page.selectOption(this.selectors.form.shippingCountry, { value: address.country });
        } catch (error) {
            console.error(`Error filling shipping address: ${error.message}`);
            throw new Error(`Failed to fill shipping address: ${error.message}`);
        }
    }

    /**
     * Continue to the shipping step
     */
    async continueToShipping() {
        console.log('Continuing to shipping step');
        
        try {
            // Take screenshot before clicking continue
            await this.page.screenshot({ path: `before-continue-to-shipping-${Date.now()}.png` });
            
            await this.page.click(this.selectors.form.continueButton);
            
            // Wait for navigation or new section to be visible
           // await this.page.waitForLoadState('networkidle');
            
            // Take screenshot after continuing
            await this.page.screenshot({ path: `after-continue-to-shipping-${Date.now()}.png` });
        } catch (error) {
            console.error(`Error continuing to shipping: ${error.message}`);
            throw new Error(`Failed to continue to shipping: ${error.message}`);
        }
    }
}

module.exports = { SalesFunnelInitialCheckoutPage }; 