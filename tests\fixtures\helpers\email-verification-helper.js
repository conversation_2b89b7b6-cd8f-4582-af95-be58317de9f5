/**
 * @fileoverview Email Verification Helper
 *
 * Centralized helper for email verification that eliminates repetitive
 * email verification patterns while maintaining compatibility with existing
 * Mailtrap integration and brand-specific email templates.
 * 
 * Features:
 * - Standardized email verification patterns
 * - Brand-specific email template handling
 * - Enhanced retry logic and error handling
 * - Support for different email types (order confirmation, abandoned cart, etc.)
 * - Graceful degradation for email verification failures
 */

/**
 * Email Verification Helper with enhanced patterns
 */
class EmailVerificationHelper {
    constructor(mailtrapHelper, testDataManager) {
        this.mailtrapHelper = mailtrapHelper;
        this.testDataManager = testDataManager;
        this.brand = testDataManager.brand;
        
        console.log(`[EmailVerificationHelper] Initialized for brand: ${this.brand}`);
    }

    /**
     * Wait for order confirmation email with brand-specific handling
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForOrderConfirmationEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for order confirmation email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 60000, // 1 minute
            attempts: 6,
            interval: 10000, // 10 seconds
            orderNumber: null // Initialize orderNumber to be potentially populated
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        let extractedOrderNumber = finalOptions.orderNumber;
        
        try {
            const searchSubject = this.getBrandOrderConfirmationSubject();
            console.log(`[EmailVerificationHelper] Searching for email with subject containing: "${searchSubject}" for customer: ${customerEmail}`);
            
            const email = await this.mailtrapHelper.waitForEmail(
                customerEmail, 
                searchSubject, 
                finalOptions.timeout / 1000, 
                finalOptions.attempts,
                finalOptions.interval / 1000
            );
            
            if (email && email.id) {
                console.log(`[EmailVerificationHelper] Email found with ID: ${email.id}. Subject: "${email.subject}"`);
                
                // Priority 1: Attempt to extract order number from HTML content
                try {
                    const htmlContent = await this.mailtrapHelper.getHtmlContent(
                        this.mailtrapHelper.inboxId, // Assuming inboxId is accessible on mailtrapHelper
                        email.id
                    );
                    if (htmlContent) {
                        const htmlOrderNumberMatch = htmlContent.match(/<span itemprop="orderNumber"[^>]*>([^<]+)<\/span>/i);
                        if (htmlOrderNumberMatch && htmlOrderNumberMatch[1]) {
                            extractedOrderNumber = htmlOrderNumberMatch[1].trim();
                            console.log(`[EmailVerificationHelper] Extracted order number "${extractedOrderNumber}" from HTML content.`);
                        } else {
                            console.warn(`[EmailVerificationHelper] Could not extract order number from HTML content itemprop span.`);
                        }
                    }
                } catch (htmlError) {
                    console.warn(`[EmailVerificationHelper] Error fetching or parsing HTML content: ${htmlError.message}`);
                }

                // Priority 2: Fallback to extracting from subject if not found in HTML (optional, could be removed if HTML is reliable)
                if (!extractedOrderNumber && email.subject) {
                    console.log("[EmailVerificationHelper] Order number not found in HTML, attempting to extract from subject as fallback.");
                    const subjectOrderNumberMatch = email.subject.match(/Order\s*#(\S+)\s*(Is Confirmed|Confirmed)/i);
                    if (subjectOrderNumberMatch && subjectOrderNumberMatch[1]) {
                        extractedOrderNumber = subjectOrderNumberMatch[1];
                        console.log(`[EmailVerificationHelper] Extracted order number "${extractedOrderNumber}" from subject.`);
                    } else {
                        console.warn(`[EmailVerificationHelper] Could not extract order number from subject either: "${email.subject}"`);
                    }
                }

                return { ...email, extractedOrderNumber }; 
            }
            
            console.warn(`[EmailVerificationHelper] Order confirmation email not found for ${customerEmail} with subject query "${searchSubject}"`);
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Order confirmation email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Wait for abandoned cart email with enhanced retry logic
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForAbandonedCartEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for abandoned cart email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 120000, // 2 minutes (abandoned cart emails may take longer)
            attempts: 12,
            interval: 10000,
            searchAfter: new Date(Date.now() - 10 * 60 * 1000).toISOString() // Last 10 minutes
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const expectedSubject = this.getBrandAbandonedCartSubject();
            
            const email = await this.mailtrapHelper.waitForEmail({
                recipient: customerEmail,
                subject: expectedSubject,
                search_after: finalOptions.searchAfter
            }, finalOptions.attempts, finalOptions.interval);
            
            if (email) {
                console.log(`[EmailVerificationHelper] Abandoned cart email found: ${email.id}`);
                return email;
            }
            
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Abandoned cart email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Wait for welcome email with brand-specific handling
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<Object|null>} Email object or null if not found
     */
    async waitForWelcomeEmail(customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Waiting for welcome email for: ${customerEmail}`);
        
        const defaultOptions = {
            timeout: 60000,
            attempts: 6,
            interval: 10000
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        try {
            const expectedSubject = this.getBrandWelcomeSubject();
            
            const email = await this.mailtrapHelper.waitForEmail(
                customerEmail,
                expectedSubject,
                finalOptions.timeout / 1000,
                finalOptions.attempts,
                finalOptions.interval / 1000
            );
            
            if (email) {
                console.log(`[EmailVerificationHelper] Welcome email found: ${email.id}`);
                return email;
            }
            
            return null;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Welcome email verification failed: ${error.message}`);
            return null;
        }
    }

    /**
     * Verify email content contains expected elements
     * @param {string} emailId - Email ID
     * @param {string[]} expectedElements - Array of expected content elements
     * @returns {Promise<boolean>} True if all elements found
     */
    async verifyEmailContent(emailId, expectedElements = []) {
        console.log(`[EmailVerificationHelper] Verifying email content for: ${emailId}`);
        
        try {
            const content = await this.mailtrapHelper.getHtmlContent(
                this.mailtrapHelper.inboxId,
                emailId
            );
            
            const missingElements = [];
            
            for (const element of expectedElements) {
                if (!content.includes(element)) {
                    missingElements.push(element);
                }
            }
            
            if (missingElements.length > 0) {
                console.warn(`[EmailVerificationHelper] Missing email content elements: ${missingElements.join(', ')}`);
                return false;
            }
            
            console.log(`[EmailVerificationHelper] All expected email content elements found`);
            return true;
        } catch (error) {
            console.error(`[EmailVerificationHelper] Email content verification failed: ${error.message}`);
            return false;
        }
    }

    /**
     * Verify email with graceful degradation
     * @param {string} emailType - Email type (order_confirmation, abandoned_cart, welcome)
     * @param {string} customerEmail - Customer email address
     * @param {Object} options - Verification options
     * @returns {Promise<boolean>} True if verification successful, false if failed gracefully
     */
    async verifyEmailWithGracefulDegradation(emailType, customerEmail, options = {}) {
        console.log(`[EmailVerificationHelper] Verifying ${emailType} email with graceful degradation`);
        let emailDetails = null; // To store the full email object including extractedOrderNumber
        
        try {
            // let email = null; // Old variable, replaced by emailDetails
            
            switch (emailType) {
                case 'order_confirmation':
                    emailDetails = await this.waitForOrderConfirmationEmail(customerEmail, options);
                    break;
                case 'abandoned_cart':
                    // Assuming waitForAbandonedCartEmail and waitForWelcomeEmail return similar structure or just the email object
                    emailDetails = await this.waitForAbandonedCartEmail(customerEmail, options);
                    break;
                case 'welcome':
                    emailDetails = await this.waitForWelcomeEmail(customerEmail, options);
                    break;
                default:
                    console.warn(`[EmailVerificationHelper] Unknown email type: ${emailType}`);
                    return { success: false, extractedOrderNumber: null }; // Return structure consistent with success
            }
            
            if (emailDetails && emailDetails.id) { // Check for email ID to confirm email was found
                let contentVerified = true;
                // Additional content verification if specified
                if (options.expectedContent && options.expectedContent.length > 0) {
                    contentVerified = await this.verifyEmailContent(emailDetails.id, options.expectedContent);
                }
                // Return success status and the potentially extracted order number
                return { 
                    success: contentVerified, 
                    extractedOrderNumber: emailDetails.extractedOrderNumber || null, 
                    emailId: emailDetails.id 
                };
            }
            
            console.warn(`[EmailVerificationHelper] ${emailType} email not found, but continuing gracefully`);
            return { success: false, extractedOrderNumber: null }; // Consistent return structure
        } catch (error) {
            console.error(`[EmailVerificationHelper] Email verification failed: ${error.message}`);
            console.log(`[EmailVerificationHelper] Continuing test execution despite email verification failure`);
            return { success: false, extractedOrderNumber: null }; // Consistent return structure
        }
    }

    /**
     * Get brand-specific order confirmation email subject
     * @returns {string} Expected email subject
     */
    getBrandOrderConfirmationSubject() {
        try {
            const subjectFromTestDataManager = this.testDataManager.getOrderConfirmationEmailSubject();

            // Specific override for 'aeons' if TestDataManager returns the problematic static subject
            if (this.brand === 'aeons') {
                if (subjectFromTestDataManager === 'AEONS Order Confirmation') {
                    console.warn(`[EmailVerificationHelper] Overriding 'aeons' static subject from TestDataManager with a more general one for searching.`);
                    return 'Is Confirmed | Aeons'; // Use the general, searchable part
                }
                // If it's not the problematic static one, but still for aeons, check if it's dynamic
                if (subjectFromTestDataManager && subjectFromTestDataManager.includes('%ORDER_NUMBER%')) {
                    console.warn(`[EmailVerificationHelper] Dynamic subject template for 'aeons' from TestDataManager: "${subjectFromTestDataManager}". Using general part.`);
                    return 'Is Confirmed | Aeons';
                }
                // Otherwise, use what TestDataManager provided for aeons if it's not the known static problematic one and not dynamic
                return subjectFromTestDataManager; 
            }

            // For other brands, if the template is dynamic (e.g., contains %ORDER_NUMBER%), use a fallback.
            if (subjectFromTestDataManager && subjectFromTestDataManager.includes('%ORDER_NUMBER%')) {
                console.warn(`[EmailVerificationHelper] Dynamic subject template for brand '${this.brand}': "${subjectFromTestDataManager}". Using fallback subject for initial search.`);
                // Fallback for other brands with dynamic subjects - ideally they provide a base part or this map is extended
                const fallbackSubjects = {
                    'dss': 'Is Confirmed | Dr. Sister Skincare', // Updated fallback for DSS
                    'ypn': 'Your Pet Nutrition Order Confirmation'    // Example, ensure this is searchable or update
                    // Add other brand fallbacks here if their TestDataManager provides %ORDER_NUMBER% templates
                };
                return fallbackSubjects[this.brand] || `${this.brand.toUpperCase()} Order Confirmation`; // Generic fallback if brand not in map
            }
            
            // If not 'aeons' and not dynamic, use the subject from TestDataManager as is
            return subjectFromTestDataManager; 

        } catch (error) {
            // Fallback to brand-specific defaults if TestDataManager fails or doesn't provide a subject
            console.warn('[EmailVerificationHelper] Could not get subject from TestDataManager, using hardcoded fallback. Error:', error.message);
            const subjects = {
                'aeons': 'Is Confirmed | Aeons', // General part for Aeons as fallback
                'dss': 'Your Dr. Sister Skincare Order Confirmation',
                'ypn': 'Your Pet Nutrition Order Confirmation'
            };
            return subjects[this.brand] || `${this.brand.toUpperCase()} Order Confirmation`;
        }
    }

    /**
     * Get brand-specific abandoned cart email subject
     * @returns {string} Expected email subject
     */
    getBrandAbandonedCartSubject() {
        const subjects = {
            'aeons': 'Complete your purchase',
            'dss': 'You left something behind | Dr. Sister Skincare',
            'ypn': 'Your cart is waiting | Your Pet Nutrition'
        };
        
        return subjects[this.brand] || 'Complete your purchase';
    }

    /**
     * Get brand-specific welcome email subject
     * @returns {string} Expected email subject
     */
    getBrandWelcomeSubject() {
        const subjects = {
            'aeons': 'Welcome To AEONS!',
            'dss': 'Welcome To Dr. Sister Skincare!',
            'ypn': 'Welcome To Your Pet Nutrition!'
        };
        
        return subjects[this.brand] || `Welcome To ${this.brand.toUpperCase()}!`;
    }

    /**
     * Get brand-specific expected email content elements
     * @param {string} emailType - Email type
     * @returns {string[]} Array of expected content elements
     */
    getBrandExpectedContent(emailType) {
        const contentMap = {
            'aeons': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['items in your cart', 'Complete Purchase'],
                'welcome': ['account login credentials', 'getting started']
            },
            'dss': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['Dark Spot Vanish', 'Complete Purchase'],
                'welcome': ['account login credentials', 'skincare routine']
            },
            'ypn': {
                'order_confirmation': ['Order Number', 'Total', 'Shipping Address'],
                'abandoned_cart': ['pet nutrition', 'Complete Purchase'],
                'welcome': ['account login credentials', 'pet care tips']
            }
        };
        
        return contentMap[this.brand]?.[emailType] || [];
    }

    /**
     * Generate test email address for the current brand
     * @param {string} prefix - Email prefix
     * @returns {string} Generated test email
     */
    generateTestEmail(prefix = 'test') {
        const timestamp = Date.now();
        return `${prefix}-${this.brand}-${timestamp}@malaberg.com`;
    }

    /**
     * Clean up test emails (if supported by email service)
     * @param {string[]} emailIds - Array of email IDs to clean up
     * @returns {Promise<void>}
     */
    async cleanupTestEmails(emailIds = []) {
        console.log(`[EmailVerificationHelper] Cleaning up ${emailIds.length} test emails`);
        
        try {
            // Implementation depends on Mailtrap API capabilities
            // This is a placeholder for future cleanup functionality
            for (const emailId of emailIds) {
                console.log(`[EmailVerificationHelper] Would clean up email: ${emailId}`);
            }
        } catch (error) {
            console.warn(`[EmailVerificationHelper] Email cleanup failed: ${error.message}`);
        }
    }
}

module.exports = { EmailVerificationHelper };
