/**
 * Environment-specific test runner for CI/CD integration
 * This script runs tests based on environment variables or command line arguments
 *
 * Usage:
 * - Direct: node run-env-tests.js <env> <browser/device> [options]
 * - From GitLab CI/CD: Use the CI_ environment variables
 *
 * Options:
 * --no-artifacts : Skip downloading artifacts after test run
 * --test-file=<path> : Specify a specific test file to run
 * --tag=<tag> : Specify a test tag (default: @stage_one_time_smoke or @dev_one_time_smoke)
 * --retries=<number> : Number of times to retry failed tests (default: 1)
 * --timeout=<number> : Test timeout in milliseconds (default: 60000 or 300000 for mobile)
 * --workers=<number> : Number of parallel workers (default: 1 for BrowserStack compatibility)
 * --use-browserstack : Force the use of BrowserStack for testing
 * --no-browserstack : Force the use of pure Playwright for testing
 */

const { execSync, spawnSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const yaml = require('yaml');

// Get Playwright version from package.json to ensure compatibility
const packageJson = require('../package.json');
const playwrightVersion = (packageJson.devDependencies && packageJson.devDependencies['@playwright/test']) ||
                        (packageJson.dependencies && packageJson.dependencies['@playwright/test']);

if (!playwrightVersion) {
  console.error('Could not determine Playwright version from package.json');
  process.exit(1);
}
console.log(`[CI Runner] Detected Playwright version: ${playwrightVersion}`);

// Supported platforms
const supportedPlatforms = [
  'windows-chrome', 'mac-safari', 'firefox', 'samsung-galaxy-s23', 'iphone-14'
];

// Load .env file if not in CI
if (!process.env.CI_PIPELINE_SOURCE) {
  try {
    require('dotenv').config();
  } catch (error) {
    console.warn('Warning: Could not load .env file');
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const options = {
  env: null,
  platform: null,
  skipArtifacts: false,
  testFile: null,
  testTag: null,
  retries: null,
  timeout: null,
  workers: 1,
  useBrowserStack: null
};

// Extract options from arguments
args.forEach(arg => {
  if (arg.startsWith('--')) {
    if (arg === '--no-artifacts') {
      options.skipArtifacts = true;
    } else if (arg === '--use-browserstack') {
      options.useBrowserStack = true;
    } else if (arg === '--no-browserstack') {
      options.useBrowserStack = false;
    } else if (arg.startsWith('--test-file=')) {
      options.testFile = arg.split('=')[1];
    } else if (arg.startsWith('--tag=')) {
      options.testTag = arg.split('=')[1];
    } else if (arg.startsWith('--retries=')) {
      options.retries = parseInt(arg.split('=')[1], 10);
    } else if (arg.startsWith('--timeout=')) {
      options.timeout = parseInt(arg.split('=')[1], 10);
    } else if (arg.startsWith('--workers=')) {
      options.workers = parseInt(arg.split('=')[1], 10);
    }
  } else if (!options.env) {
    options.env = arg;
  } else if (!options.platform) {
    options.platform = arg;
  }
});

// Get environment from command line args or CI env vars
const env = options.env || process.env.TEST_ENV || 'stage';
const platform = options.platform || process.env.PLATFORM || 'windows-chrome';

// Validate platform
if (!supportedPlatforms.includes(platform)) {
  console.error(`Error: Invalid platform: ${platform}`);
  console.log('Available platforms:');
  console.log('Desktop:', supportedPlatforms.filter(p => !p.includes('galaxy') && !p.includes('iphone')).join(', '));
  console.log('Mobile:', supportedPlatforms.filter(p => p.includes('galaxy') || p.includes('iphone')).join(', '));
  process.exit(1);
}

// Determine if this is a mobile device
const isMobile = platform.includes('galaxy') ||
                platform.includes('iphone') ||
                platform.includes('android') ||
                platform.includes('ios');

// Determine if this is a BrowserStack test
const hasBrowserStackCredentials = process.env.BROWSERSTACK_USERNAME && process.env.BROWSERSTACK_ACCESS_KEY;
const isBrowserStack = options.useBrowserStack !== null ? options.useBrowserStack : hasBrowserStackCredentials;

// Set build name for BrowserStack
const timestamp = new Date().toISOString().replace(/[:.]/g, '_');
const buildName = `ci_${env}_${platform}_${timestamp}`;

// Set default timeout based on device type
const defaultTimeout = isMobile ? 300000 : 60000;
const testTimeout = options.timeout || process.env.TEST_TIMEOUT || defaultTimeout;

// Set default retries
const retries = options.retries || process.env.RETRY_COUNT || 1;

// Set the test file to run (default to smoke tests)
const defaultTestFile = 'main-purchase.spec.js';
const testFile = options.testFile || defaultTestFile;

// Set environment-specific test tag
const testTag = options.testTag || (env === 'dev' ? '@dev_one_time_smoke' : '@stage_one_time_smoke');

// All platform capabilities are defined here, making this the single source of truth.
const platformCapabilities = {
  'windows-chrome': {
    os: 'Windows',
    os_version: '11',
    browser: 'chrome',
    browser_version: 'latest',
    'browserstack.playwrightVersion': playwrightVersion.replace('^', ''),
    'client.playwrightVersion': playwrightVersion.replace('^', ''),
  },
  'mac-safari': {
    os: 'OS X',
    os_version: 'Sonoma',
    browser: 'safari',
    browser_version: 'latest',
    'browserstack.playwrightVersion': playwrightVersion.replace('^', ''),
    'client.playwrightVersion': playwrightVersion.replace('^', ''),
  },
  'firefox': { // Assuming firefox runs on windows
    os: 'Windows',
    os_version: '11',
    browser: 'firefox',
    browser_version: 'latest',
    'browserstack.playwrightVersion': playwrightVersion.replace('^', ''),
    'client.playwrightVersion': playwrightVersion.replace('^', ''),
  },
  'samsung-galaxy-s23': {
    device: 'Samsung Galaxy S23',
    os_version: '13.0',
    browserName: 'chrome',
    realMobile: true,
    'browserstack.playwrightVersion': playwrightVersion.replace('^', ''),
    'client.playwrightVersion': playwrightVersion.replace('^', ''),
  },
  'iphone-14': {
    device: 'iPhone 14',
    os_version: '16',
    browserName: 'safari',
    realMobile: true,
    'browserstack.playwrightVersion': playwrightVersion.replace('^', ''),
    'client.playwrightVersion': playwrightVersion.replace('^', ''),
  },
};

// For local runs, the project name in playwright.config.js matches the platform name.
// For BrowserStack, we will generate a config with a single, known project name.
const projectName = isBrowserStack ? 'browserstack-project' : platform;

// --- Dynamic BrowserStack Config Generation ---
let bsConfigFile = 'browserstack.yml'; // Default for local runs
const tempConfigPath = path.join(__dirname, '..', 'browserstack.dynamic.yml');

if (isBrowserStack) {
  try {
    // Load credentials from the base config file.
    const baseConfigPath = path.join(__dirname, '..', 'browserstack.base.yml');
    const baseConfigContent = fs.readFileSync(baseConfigPath, 'utf8');
    const baseConfig = yaml.parse(baseConfigContent);

    // Construct the entire configuration object dynamically.
    // This prevents stray settings from interfering.
    const dynamicConfig = {
      userName: baseConfig.userName || process.env.BROWSERSTACK_USERNAME,
      accessKey: baseConfig.accessKey || process.env.BROWSERSTACK_ACCESS_KEY,
      buildName: buildName,
      projectName: `Playwright-Test-${env}`,
      debug: true,
      networkLogs: true,
      consoleLogs: 'debug',
      framework: 'playwright',
      platforms: [{
        name: projectName,
        ...platformCapabilities[platform],
      }],
      parallelsPerPlatform: 1,
      maxParallelSessions: 2,
      local: false,
      accessibility: false,
    };

    fs.writeFileSync(tempConfigPath, yaml.stringify(dynamicConfig));
    bsConfigFile = tempConfigPath;
    console.log(`Dynamically generated BrowserStack config at: ${bsConfigFile}`);
  } catch (error) {
    console.error(`Failed to generate dynamic BrowserStack config: ${error.message}`);
    process.exit(1);
  }
}
// --- End of Dynamic Config Generation ---

// Construct the test command
const command = [
  'npx',
  'cross-env',
  `TEST_ENV=${env}`,
  `PLATFORM=${platform}`,
  `BUILD_NAME=${buildName}`,
  `TEST_TIMEOUT=${testTimeout}`,
  `RETRY_COUNT=${retries}`,
  `IS_MOBILE=${isMobile ? 'true' : 'false'}`,
  isBrowserStack ? 'BROWSERSTACK_SDK_ENABLED=true' : '',
  isBrowserStack ? 'BROWSERSTACK_DEBUG=true' : '',
  // Add real_mobile flag for mobile devices
  isMobile && isBrowserStack ? 'BROWSERSTACK_REAL_MOBILE=true' : '',
  // Set the BrowserStack config file
  isBrowserStack ? `BROWSERSTACK_CONFIG_FILE=${bsConfigFile}` : '',
  'npx',
  isBrowserStack ? 'browserstack-node-sdk' : '',
  'playwright',
  'test',
  testFile,
  // For BrowserStack, we generate a config with a single project, so we don't need to specify one.
  // For local runs, we need to specify the project to match the platform.
  isBrowserStack ? '' : `--project=${projectName}`,
  (!options.testFile || options.testTag) ? `--grep="${testTag}"` : '',
  `--workers=${options.workers}`,
  `--retries=${retries}`
].filter(Boolean);

// Log execution details
console.log('CI Test Runner Configuration:');
console.log('--------------------------------------------------');
console.log(`Environment:    ${env}`);
console.log(`Platform:       ${platform}`);
console.log(`Device Type:    ${isMobile ? 'Mobile' : 'Desktop'}`);
console.log(`Test File:      ${testFile}`);
console.log(`Test Tag:       ${testTag}`);
console.log(`Using:          ${isBrowserStack ? 'BrowserStack SDK' : 'Local Playwright'}`);
console.log(`Build Name:     ${buildName}`);
console.log(`Timeout:        ${testTimeout}ms`);
console.log(`Retries:        ${retries}`);
console.log(`Workers:        ${options.workers}`);
console.log('--------------------------------------------------');
console.log(`Command: ${command.join(' ')}`);
console.log('==================================================');

try {
  console.log('Running with environment variables:');
  console.log(`PLATFORM: ${platform}`);
  console.log(`IS_MOBILE: ${isMobile ? 'true' : 'false'}`);
  console.log(`BROWSERSTACK_REAL_MOBILE: ${isMobile && isBrowserStack ? 'true' : 'false'}`);

  // To handle spaces in arguments correctly (especially for Windows),
  // we use spawnSync instead of execSync. We need to separate the command
  // from its arguments.
  const mainCommand = command[0]; // 'npx'
  const args = command.slice(1);

  const result = spawnSync(mainCommand, args, {
    stdio: 'inherit',
    shell: true, // Use shell to properly resolve 'npx' and other commands
    env: {
      ...process.env,
      TEST_ENV: env,
      PLATFORM: platform,
      BUILD_NAME: buildName,
      TEST_TIMEOUT: testTimeout.toString(),
      RETRY_COUNT: retries.toString(),
      IS_MOBILE: isMobile ? 'true' : 'false',
      BROWSERSTACK_SDK_ENABLED: isBrowserStack ? 'true' : 'false',
      BROWSERSTACK_DEBUG: isBrowserStack ? 'true' : 'false',
      BROWSERSTACK_REAL_MOBILE: isMobile && isBrowserStack ? 'true' : 'false',
    },
  });

  // Cleanup the dynamic config file after the run
  if (isBrowserStack && fs.existsSync(tempConfigPath)) {
    try {
      fs.unlinkSync(tempConfigPath);
      console.log(`Cleaned up temporary config file: ${tempConfigPath}`);
    } catch (error) {
      console.warn(`Warning: Failed to delete temporary config file ${tempConfigPath}: ${error.message}`);
    }
  }

  if (result.status !== 0) {
    const errorMessage = result.error ? result.error.message : `Command failed with exit code ${result.status}`;
    console.error(`\nTest run failed: ${errorMessage}`);
    console.log('\nAvailable platforms:');
    console.log('Desktop:', supportedPlatforms.filter(p => !p.includes('galaxy') && !p.includes('iphone')).join(', '));
    console.log('Mobile:', supportedPlatforms.filter(p => p.includes('galaxy') || p.includes('iphone')).join(', '));
    process.exit(1);
  }
} catch (error) {
  console.error(`\nAn unexpected error occurred: ${error.message}`);
  process.exit(1);
}

/**
 * Extracts and prints the projects from the BrowserStack SDK generated config file
 * This will help us understand the exact project names that BrowserStack SDK creates
 */
function extractAndPrintBrowserStackProjects() {
  const tempConfigPath = path.join(__dirname, '..', 'playwright-browserstack-sdk.config.js');

  try {
    if (fs.existsSync(tempConfigPath)) {
      console.log('\nExamining BrowserStack SDK generated config file for actual project names:');
      const configContent = fs.readFileSync(tempConfigPath, 'utf8');

      // Find the projects section in the file
      const projectsMatch = configContent.match(/projects\s*:\s*\[([\s\S]*?)\]/);
      if (projectsMatch && projectsMatch[1]) {
        const projectsSection = projectsMatch[1];

        // Find all project entries
        const projectEntries = projectsSection.match(/{\s*name\s*:\s*['"]([^'"]+)['"]/g);

        if (projectEntries && projectEntries.length > 0) {
          console.log('Found BrowserStack SDK generated projects:');
          projectEntries.forEach(entry => {
            const nameMatch = entry.match(/name\s*:\s*['"]([^'"]+)['"]/);
            if (nameMatch && nameMatch[1]) {
              console.log(`- Project: "${nameMatch[1]}"`);
            }
          });
        } else {
          console.log('No project entries found in the BrowserStack SDK config');
        }
      } else {
        console.log('Could not find projects section in BrowserStack SDK config');
      }
    } else {
      console.log('BrowserStack SDK config file not found. It will be generated during test run.');
    }
  } catch (error) {
    console.error('Error examining BrowserStack SDK config:', error.message);
  }
}

// Call the function before test execution if using BrowserStack
if (isBrowserStack) {
  extractAndPrintBrowserStackProjects();
}

// Download artifacts if needed
if (!options.skipArtifacts && isBrowserStack) {
  console.log('\nDownloading test artifacts from BrowserStack...');
  // Implement artifact download logic here
}
