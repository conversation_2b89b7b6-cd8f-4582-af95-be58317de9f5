import { test } from 'magnitude-test';

/**
 * YourPetNutrition Cart Functionality and Story Page Tests
 * Based on Test_cases.md sections 3 and 11
 */

test.group('YourPetNutrition Cart and Story Page', () => {
    
    test('Story page video verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Story page')
        .step('Verify video content')
            .check('Video is present on the Story page')
            .check('Video is the correct Your Pet Nutrition video, not Dr Sister video')
            .check('Video plays correctly when clicked')
        .step('Verify story page content')
            .check('Story page contains information about the company')
            .check('Content about Dr. <PERSON> is displayed')
            .check('Company mission and values are clearly presented');

    test('Add Canine Prime to cart', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Shop Dogs page')
        .step('Click on Canine Prime product')
        .step('Select purchase options')
            .data({ 
                purchaseType: 'oneTime',
                quantity: '1 jar'
            })
        .step('Add product to cart')
            .check('Add to cart button is clickable')
            .check('Product is successfully added to cart')
            .check('Cart icon updates to show item count')
        .step('Verify cart contents')
            .check('Cart contains Canine Prime product')
            .check('Correct quantity is displayed in cart')
            .check('Correct price is displayed in cart');

    test('Add Denta Soft with different quantities to cart', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Shop Dogs page')
        .step('Click on Denta Soft product')
        .step('Test quantity selection and pricing')
            .data({ quantity: '3 jars' })
            .check('Price updates correctly when selecting 3 jars')
            .check('Discount information is displayed for bulk purchase')
        .step('Add 3 jars to cart')
            .check('Product with correct quantity is added to cart')
            .check('Cart total reflects the correct quantity and price')
        .step('Update quantity in cart')
            .data({ newQuantity: '2' })
            .check('Quantity can be updated in cart')
            .check('Cart total updates accordingly');

    test('Add subscription product to cart', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to Shop Dogs page')
        .step('Click on Relax and Restore product')
        .step('Select subscription option')
            .data({ 
                purchaseType: 'subscription',
                frequency: '2 months'
            })
            .check('SUBSCRIBE AND SAVE option is selected')
            .check('Frequency dropdown shows correct selection')
            .check('Subscription discount is applied and displayed')
        .step('Add subscription product to cart')
            .check('Product with subscription is added to cart')
            .check('Cart shows subscription details and frequency')
            .check('Discounted price is reflected in cart total');

    test('Cart page functionality verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Add a product to cart for testing')
            .data({ 
                product: 'Denta Soft',
                quantity: '1 jar'
            })
        .step('Navigate to cart page')
            .check('Cart page loads correctly')
            .check('Cart title "Your cart" is displayed')
        .step('Verify cart contents display')
            .check('Product name is displayed correctly')
            .check('Product image is visible')
            .check('Product price is displayed')
            .check('Quantity selector is functional')
        .step('Verify cart totals')
            .check('Subtotal is calculated correctly')
            .check('Shipping information is displayed')
            .check('Total amount is calculated correctly')
        .step('Verify checkout functionality')
            .check('CHECK OUT button is visible and clickable')
            .check('Checkout process can be initiated');

    test('Multiple products in cart verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Add first product to cart')
            .data({ 
                product: 'Canine Prime',
                quantity: '1 jar'
            })
        .step('Add second product to cart')
            .data({ 
                product: 'Denta Soft',
                quantity: '2 jars'
            })
        .step('Verify multiple products in cart')
            .check('Both products are displayed in cart')
            .check('Individual quantities are correct')
            .check('Individual prices are correct')
            .check('Cart subtotal includes both products')
        .step('Test quantity updates for multiple products')
            .check('Quantity can be updated for each product independently')
            .check('Cart total updates correctly when quantities change')
        .step('Test product removal from cart')
            .check('Products can be removed from cart individually')
            .check('Cart total updates when products are removed');

    test('Empty cart state verification', { url: 'https://yourpetnutrition.myshopify.com/' })
        .step('Navigate to cart page when empty')
            .check('Empty cart message is displayed appropriately')
            .check('Continue shopping link or button is available')
        .step('Add product and then remove it')
            .data({ product: 'Feline 40' })
            .check('Cart updates from empty to containing product')
            .check('Cart returns to empty state when product is removed')
            .check('Empty cart state is handled gracefully');
}); 